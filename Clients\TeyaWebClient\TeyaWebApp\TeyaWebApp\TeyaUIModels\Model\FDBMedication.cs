﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class FDBMedication : IModel
    {
        public string MEDID { get; set; }
        public string ROUTED_DOSAGE_FORM_MED_ID { get; set; }
        public string? MED_STRENGTH { get; set; }
        public string? MED_STRENGTH_UOM { get; set; }
        public string MED_MEDID_DESC { get; set; }
        public string GCN_SEQNO { get; set; }
        public string MED_GCNSEQNO_ASSIGN_CD { get; set; }
        public string MED_NAME_SOURCE_CD { get; set; }
        public string MED_REF_FED_LEGEND_IND { get; set; }
        public string MED_REF_DEA_CD { get; set; }
        public string MED_REF_MULTI_SOURCE_CD { get; set; }
        public string MED_REF_GEN_DRUG_NAME_CD { get; set; }
        public string MED_REF_GEN_COMP_PRICE_CD { get; set; }
        public string MED_REF_GEN_SPREAD_CD { get; set; }
        public string MED_REF_INNOV_IND { get; set; }
        public string MED_REF_GEN_THERA_EQU_CD { get; set; }
        public string MED_REF_DESI_IND { get; set; }
        public string MED_REF_DESI2_IND { get; set; }
        public string MED_STATUS_CD { get; set; }
        public string? GENERIC_MEDID { get; set; }
    }
}
