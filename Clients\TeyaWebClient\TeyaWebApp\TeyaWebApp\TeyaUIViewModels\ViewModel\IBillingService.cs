﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IBillingService
    {
        Task<IEnumerable<Billing>> GetAllBillingsAsync();
        Task<Billing> GetBillingByIdAsync(Guid id);
        Task<IEnumerable<Billing>> GetBillingsByPatientIdAsync(Guid patientId);
        Task AddBillingAsync(Billing billing);
        Task UpdateBillingAsync(Billing billing);
        Task DeleteBillingByIdAsync(Guid id);
    }
}
