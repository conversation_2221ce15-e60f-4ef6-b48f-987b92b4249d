﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IExaminationService
    {
        Task<List<Examination>> GetExaminationByPatientId(Guid PatientId, Guid? OrgID, bool Subscription);
        Task<List<Examination>> GetExaminationByPatientIdAsyncAndIsActive(Guid PatientId, Guid? OrgID, bool Subscription);
        Task AddExaminationAsync(List<Examination> examinations, Guid? OrgID, bool Subscription);
        Task UpdateExaminationListAsync(List<Examination> examinations, Guid? OrgID, bool Subscription);
    }
}
