﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Microsoft.Graph.Models.Security;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;

public class GynHistoryService : IGynHistoryService
{
    private readonly HttpClient _httpClient;
    private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
    private readonly string _gynHistoryUrl;
    private readonly ITokenService _tokenService;
    private readonly ILogger<GynHistoryService> _logger;

    public GynHistoryService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService, ILogger<GynHistoryService> logger)
    {
        _httpClient = httpClient;
        _localizer = localizer;
        _logger = logger;

        _gynHistoryUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
        _tokenService = tokenService;
    }



    public async Task AddAsync(GynHistoryDTO gynHistoryDto, Guid? OrgID, bool Subscription)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PostAsJsonAsync($"{_gynHistoryUrl}/api/GynHistory/{OrgID}/{Subscription}", gynHistoryDto);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding GYN history");
            throw new Exception("Error adding GYN history: " + ex.Message);
        }
    }

    public async Task UpdateGynHistoryAsync(Guid id, GynHistoryDTO gynHistoryDto, Guid? OrgID, bool Subscription)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"{_gynHistoryUrl}/api/GynHistory/{id}/{OrgID}/{Subscription}", gynHistoryDto);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating GYN history");
            throw new Exception("Error updating GYN history: " + ex.Message);
        }
    }

    public async Task DeleteGynHistoryAsync(Guid id, Guid? OrgID, bool Subscription)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.DeleteAsync($"{_gynHistoryUrl}/api/GynHistory{id}/{OrgID}/{Subscription}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting GYN history");
            throw new Exception("Error deleting GYN history: " + ex.Message);
        }
    }

    public async Task<IEnumerable<GynHistoryDTO>> GetByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription)
    {
        try
        {
            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            return await _httpClient.GetFromJsonAsync<IEnumerable<GynHistoryDTO>>($"{_gynHistoryUrl}/api/GynHistory/patient/{patientId}/{OrgID}/{Subscription}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error retrieving GYN histories for PatientId {patientId}");
            throw new Exception($"Error retrieving GYN histories for PatientId {patientId}: " + ex.Message);
        }
    }

    public async Task UpdateGynHistoryListAsync(List<GynHistoryDTO> gynHistories, Guid? OrgID, bool Subscription)
    {
        try
        {
            if (gynHistories == null || !gynHistories.Any())
            {
                throw new ArgumentException("OB history list is empty.", nameof(gynHistories));
            }

            var token = _tokenService.AccessToken;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await _httpClient.PutAsJsonAsync($"{_gynHistoryUrl}/api/GynHistory/bulk-update/{OrgID}/{Subscription}", gynHistories);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during bulk update of GYN histories");
            throw new Exception("Error during bulk update of GYN histories: " + ex.Message);
        }
    }

    

    public async Task<List<GynHistoryDTO>> LoadGynHistoriesAsync(Guid patientId, Guid? OrgID, bool Subscription)
    {
        try
        {

            var obHistories = await GetByPatientIdAsync(patientId, OrgID, false);


            return obHistories.Select(h => new GynHistoryDTO
            {
                gynId = h.gynId,
                PatientId = h.PatientId,
                Symptoms = h.Symptoms,
                Notes = h.Notes,
                DateOfHistory = h.DateOfHistory,
                OrganizationId = h.OrganizationId,
                PcpId = h.PcpId,
                IsDeleted = h.IsDeleted
            }).ToList();
        }
        catch (Exception ex)
        {

            throw new Exception("Error loading OB histories: " + ex.Message);
        }
    }

}