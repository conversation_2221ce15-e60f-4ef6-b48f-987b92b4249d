﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Tasks:IModel
    {
        public Guid Id { get; set; }
        public Guid OrganizationId { get; set; }
        public string SSN { get; set; }
        public string? PatientName { get; set; }
        public string? TaskType { get; set; }
        public string? Subject { get; set; }
        public string? AssignedTo { get; set; }
        public string? Status { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? DueDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? Priority { get; set; }
        public string? Notes { get; set; }
        public bool? RecurringAction { get; set; }
        public DateTime? LastDueDate { get; set; }
        public DateTime? LastVisitDate { get; set; }
        public int? Frequency { get; set; }
        public bool Subscription { get; set; }
    }
}
