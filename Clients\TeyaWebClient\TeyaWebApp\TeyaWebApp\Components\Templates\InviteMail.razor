﻿@page "/LoginEmail"
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using TeyaWebApp.TeyaAIScribeResource
@inject IStringLocalizer<TeyaAIScribeResource> _localizer
@inject ILogger<InviteMail> _logger
@inject InviteMailParametersService _inviteMailParametersService
@inject IConfiguration Configuration


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@_localizer["Title"]</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f9;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            font-size: 28px;
        }

        p {
            color: #34495e;
            font-size: 16px;
            line-height: 1.5;
        }

        .highlight {
            color: #3498db;
            font-weight: bold;
        }

        .cta-button {
            background-color: #3498db;
            color: white;
            padding: 15px 25px;
            font-size: 18px;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            display: block;
            margin-top: 20px;
            width: fit-content;
        }

            .cta-button:hover {
                background-color: #2980b9;
            }

        .footer {
            text-align: center;
            font-size: 12px;
            color: #95a5a6;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">

        <h1>@_localizer["WelcomeHeading"]</h1>

        <p>@_localizer["GreetingMessage"] <span class="highlight">@Email</span>,</p>

        <p>@_localizer["CongratulationsMessage"]</p>

        <p>@_localizer["BenefitsMessage"]</p>

        <p>@_localizer["CallToActionMessage"]</p>

        <a href="@loginUrl" class="cta-button">@_localizer["LoginButtonText"]</a>

        <p>@_localizer["LoginDetailsMessage"]</p>
        <p><strong>@_localizer["EmailLabel"]:</strong> @Email</p>
        <p><strong>@_localizer["PasswordLabel"]:</strong> @Password</p>

        <div class="footer">
            <p>@_localizer["SupportMessage"]</p>
            <p>@_localizer["ThankYouMessage"]</p>
        </div>
    </div>
</body>
</html>
