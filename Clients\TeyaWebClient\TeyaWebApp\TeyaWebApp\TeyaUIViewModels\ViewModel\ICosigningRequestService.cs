﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningRequestService
    {
        // Create and manage requests
        Task<Guid> CreateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription);
        Task UpdateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription);
        Task CancelRequestAsync(Guid requestId, Guid organizationId, bool subscription);

        // Get requests for different scenarios
        Task<IEnumerable<CosigningRequest>> GetByRequesterIdAsync(Guid requesterId, Guid organizationId, bool subscription);
        Task<IEnumerable<CosigningRequest>> GetByReviewerIdAsync(Guid reviewerId, Guid organizationId, bool subscription);
        Task<CosigningRequest> GetByIdAsync(Guid requestId, Guid organizationId, bool subscription);
        Task<IEnumerable<CosigningRequest>> GetByRecordIdAsync(Guid recordId, Guid organizationId, bool subscription);

        // Review actions
        Task ApproveRequestAsync(Guid requestId, Guid reviewerId, string reviewerName, Guid organizationId, bool subscription);
        Task RequestChangesAsync(Guid requestId, Guid reviewerId, string reviewerName, string comment, Guid organizationId, bool subscription);

        // Comments
        Task AddCommentAsync(Guid requestId, Guid commenterId, string commenterName, string comment, Guid organizationId, bool subscription);
        Task ResolveCommentAsync(Guid requestId, Guid commentId, Guid resolvedById, string resolvedByName, Guid organizationId, bool subscription);

        // Utility methods
        Task<bool> HasPendingRequestAsync(Guid recordId, Guid organizationId, bool subscription);
        Task<int> GetPendingRequestCountAsync(Guid reviewerId, Guid organizationId, bool subscription);
        Task<CosigningRequest> GetActiveRequestAsync(Guid recordId, Guid organizationId, bool subscription);
    }
}
