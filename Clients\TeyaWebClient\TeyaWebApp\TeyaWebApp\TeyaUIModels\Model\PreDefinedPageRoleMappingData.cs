﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PreDefinedPageRoleMappingData : IModel
    {
        public Guid Id { get; set; }
        public string PagePath { get; set; }
        public Guid RoleId { get; set; }
        public string RoleName { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public bool IsActive { get; set; } = true;
        public bool HasAccess { get; set; }
        public bool IsModified { get; set; }
    }
}
