﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using TeyaUIViewModels.TeyaUIViewModelResources;
using DotNetEnv;
using TeyaWebApp.Services;

namespace TeyaUIViewModels.ViewModel
{
    public class SoapNotesComponentsService : ISoapNotesComponentsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotesURL;
        private readonly ILogger<SoapNotesComponent> _logger;
        private readonly ITokenService _tokenService;

        public SoapNotesComponentsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ILogger<SoapNotesComponent> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _EncounterNotesURL = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<SoapNotesComponent>> GetAllDetailsAsync()
        {
            {
                var apiUrl = $"{_EncounterNotesURL}/api/SoapNotesComponents";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<SoapNotesComponent>>();
                }
                else
                {
                    throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
                }
            }

        }
    }
}

