﻿using Microsoft.AspNetCore.Builder;
using Syncfusion.Blazor;
namespace TeyaWebApp
{
    public class Startup
    {
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IServiceCollection services)
        {
            services.AddHttpContextAccessor();
            if (env.IsDevelopment())
            {
                app.UseForwardedHeaders();
                app.UseWebAssemblyDebugging();
            }
            else
            {
                app.UseForwardedHeaders();
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseAntiforgery();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapBlazorHub();
                endpoints.MapFallbackToPage("/_Host");
            });
        }


    }
}


