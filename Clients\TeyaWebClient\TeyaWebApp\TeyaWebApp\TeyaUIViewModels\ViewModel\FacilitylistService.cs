﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DotNetEnv;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class FacilitylistService : IFacilitylistService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _FacilitylistService;
        private readonly IStringLocalizer<FacilitylistService> _localizer;
        private readonly ILogger<FacilitylistService> _logger;
        private bool HasAccess { get; set; }
        public FacilitylistService(HttpClient httpClient, IStringLocalizer<FacilitylistService> localizer, ILogger<FacilitylistService> logger, ITokenService tokenService)
        {
            _tokenService = tokenService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _FacilitylistService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Gets all Facilitylists.
        /// </summary>
        public async Task<List<Facilitydata>> GetAllFacilitylistsAsync()
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_FacilitylistService}/api/Facilitylist";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<Facilitydata>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllFacilitylists"]);
                throw;
            }
        }

        /// <summary>
        /// Gets all Facility names from Facilitylist.
        /// </summary>
        public async Task<List<string>> GetAllFacilityNamesAsync()
        {
            try
            {
                var FacilityList = await GetAllFacilitylistsAsync();
                return FacilityList.Select(Facility => Facility.Facilityname).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingFacilityNames"]);
                throw;
            }
        }

    }
}
