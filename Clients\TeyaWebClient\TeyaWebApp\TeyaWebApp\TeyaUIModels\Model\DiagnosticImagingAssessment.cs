﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class DiagnosticImagingAssessment : IModel
    {
        public Guid ID { get; set; }
        public Guid DiagnosticImagingID { get; set; }
        public Guid? OrganizationID { get; set; }

        public Guid AssessmentID { get; set; }
        [ForeignKey("DiagnosticImagingID")]
        public virtual DiagnosticImagingDTO? DiagnosticImaging { get; set; }
    }
}
