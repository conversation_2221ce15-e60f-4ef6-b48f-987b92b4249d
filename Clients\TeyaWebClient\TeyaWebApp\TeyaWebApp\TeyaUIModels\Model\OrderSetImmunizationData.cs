﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;

namespace TeyaUIModels.Model
{
    public class OrderSetImmunizationData : IModel
    {
        public Guid ImmunizationId { get; set; }
        public string Immunizations { get; set; }
        public string CPTCode { get; set; }
        public string CVXCode { get; set; }
        public string? Comments { get; set; }
        public string CPTDescription { get; set; }
        public DateTime? GivenDate { get; set; }
        public Guid OrderSetId { get; set; }

    }
}
