﻿﻿using System;
using System.Collections.Generic;
using System.Text.Json;

namespace TeyaUIModels.Model
{
    public class CosigningRequest : IModel
    {
        public Guid Id { get; set; }
        public Guid RecordId { get; set; } // Progress Note or Document ID
        public Guid RequesterId { get; set; } // Provider A who created the note
        public Guid ReviewerId { get; set; } // Provider B who will review
        public string RequesterName { get; set; }
        public string ReviewerName { get; set; }
        public CosigningRequestStatus Status { get; set; } = CosigningRequestStatus.Pending;
        public string CommentsJson { get; set; } = "[]"; // JSON array of comments
        public DateTime RequestedDate { get; set; }
        public DateTime? ReviewedDate { get; set; }
        public DateTime? LastUpdated { get; set; }
        public Guid OrganizationId { get; set; }
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;

     
        public string PatientName { get; set; }
        public string PatientAge { get; set; }
        public string PatientGender { get; set; }
        public DateTime RecordDate { get; set; }


    }

    public enum CosigningRequestStatus
    {
        Pending = 0,        // Initial state - waiting for review
        Approved = 1,       // Reviewer approved - requester can now lock the note
        ChangesRequested = 2 // Reviewer requested changes - requester needs to resolve and re-request
    }
}
