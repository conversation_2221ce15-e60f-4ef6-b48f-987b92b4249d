﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFDBService 
    {
        Task<List<FDBMedicationName>> GetAllFDBMedications();
        Task<List<FDBMedicationName>> GetFDBMedicationBySearchTerm(string SearchTerm);
        Task<List<FDBRoutedMedication>> GetFDBRoutedMedications(string str);
        Task<List<FDBRoutedDosageFormMedication>> GetFDBRoutedDosageFormMedications(string str);
        Task<List<FDBMedication>> GetFDBFinalMedications(string str);
        Task<List<FDBRouteLookUp>> GetFDBRouteLookUp();
        Task<List<FDBTakeLookUp>> GetFDBTakeLookUp();
        Task<List<FDBAllergies>> GetAllergies();
        Task<List<FDBAllergies>> GetAllergiesBySearchTerm(string SearchTerm);
        Task<List<FDB_ICD>> GetICD();
        Task<List<FDB_ICD>> GetICDBySearchTerm(string searchterm);
        Task<List<FDBVaccines>> GetVaccines();
        Task<List<FDBVaccines>> GetVaccinesBySearchTerm(string searchterm);
        Task<FDBVaccine_CPT_CVX> GetCPTForVaccine(string str);
    }
}
