﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class PageRoleMappingService : IPageRoleMappingService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<PageRoleMappingService> _localizer;
        private readonly ILogger<PageRoleMappingService> _logger;

        public PageRoleMappingService(HttpClient httpClient, IStringLocalizer<PageRoleMappingService> localizer, ILogger<PageRoleMappingService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<PageRoleMappingData>> GetPageRoleMappingsAsync()
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_MemberService}/api/PageRoleMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PageRoleMappingData>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllPageRoleMappings"]);
                throw;
            }
        }

        public async Task<PageRoleMappingData> GetPageRoleMappingByIdAsync(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PageRoleMapping/{id}/{OrgID}/{Subscription}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<PageRoleMappingData>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPageRoleMappingById"], id);
                throw;
            }
        }

        public async Task AddPageRoleMappingAsync(object pageRoleMappingData)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = JsonSerializer.Serialize(pageRoleMappingData);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/PageRoleMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddPageRoleMappingFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingPageRoleMapping"]);
                throw;
            }
        }

        public async Task UpdatePageRoleMappingAsync(object pageRoleMappingData)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();

                // Check if it's a List<PageRoleMappingData> or single PageRoleMappingData
                if (pageRoleMappingData is List<PageRoleMappingData> pageRoleMappings)
                {
                    // Handle multiple page role mappings
                    if (pageRoleMappings.Any())
                    {
                        _logger.LogInformation($"Updating {pageRoleMappings.Count} PageRoleMappings");

                        var updateTasks = pageRoleMappings.Select(async mapping =>
                        {
                            var apiUrl = $"{_MemberService}/api/PageRoleMapping/{mapping.Id}";
                            var bodyContent = JsonSerializer.Serialize(mapping);
                            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                            {
                                Content = content
                            };
                            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                            var response = await _httpClient.SendAsync(requestMessage);

                            if (response.IsSuccessStatusCode)
                            {
                                _logger.LogInformation(_localizer["PageRoleMappingUpdatedSuccessfully"], mapping.Id);
                            }
                            else
                            {
                                var errorContent = await response.Content.ReadAsStringAsync();
                                _logger.LogError($"Update failed for ID {mapping.Id}. Status Code: {response.StatusCode}, Response: {errorContent}");
                                throw new HttpRequestException(_localizer["PageRoleMappingUpdateFailed"]);
                            }
                        }).ToList();

                        await Task.WhenAll(updateTasks);
                        _logger.LogInformation($"Successfully updated {pageRoleMappings.Count} PageRoleMappings");
                    }
                }
                else if (pageRoleMappingData is PageRoleMappingData pageRoleMapping)
                {
                    // Handle single page role mapping
                    var apiUrl = $"{_MemberService}/api/PageRoleMapping/{pageRoleMapping.Id}";
                    var bodyContent = JsonSerializer.Serialize(pageRoleMapping);
                    var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                    var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                    {
                        Content = content
                    };
                    requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await _httpClient.SendAsync(requestMessage);

                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation(_localizer["PageRoleMappingUpdatedSuccessfully"], pageRoleMapping.Id);
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                        throw new HttpRequestException(_localizer["PageRoleMappingUpdateFailed"]);
                    }
                }
                else
                {
                    throw new ArgumentException("Invalid data type. Expected PageRoleMappingData or List<PageRoleMappingData>");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPageRoleMapping"]);
                throw;
            }
        }

        public async Task DeletePageRoleMappingByIdAsync(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_MemberService}/api/PageRoleMapping/{id}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PageRoleMappingDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PageRoleMappingDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPageRoleMapping"], id);
                throw;
            }
        }
        public async Task<IEnumerable<PageRoleMappingData>> GetPagesByRoleIdAsync(Guid roleId, Guid OrgID, bool Subscription)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/PageRoleMapping/role/{roleId}/{OrgID}/{Subscription}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<IEnumerable<PageRoleMappingData>>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetPagesByRoleIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPagesByRoleId"], roleId);
                throw;
            }
        }
        public async Task<IEnumerable<string>> GetRolesByPagePathAsync(string pagePath, Guid organizationId, bool Subscription)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pagePath))
                {
                    _logger.LogWarning(_localizer["PagePathRequired"]);
                    throw new ArgumentException(_localizer["PagePathRequiredMessage"]);
                }
                var apiUrl = $"{_MemberService}/api/PageRoleMapping/roles-by-pagepath?pagePath={Uri.EscapeDataString(pagePath)}&OrganizationId={organizationId}&Subscription={Subscription}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to fetch roles. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RolesNotFoundMessage"]);
                }

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<string>>(responseData, options) ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingRoles"], pagePath);
                throw;
            }
        }
    }
}
