using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningStateService
    {
        event Action<CosigningRequest> RequestUpdated;
        event Action<Guid> RequestApproved;
        event Action<Guid> RequestChangesRequested;
        event Action<Guid, Guid> CommentResolved;
        event Action RequestCountsChanged;

        Task NotifyRequestUpdated(CosigningRequest request);
        Task NotifyRequestApproved(Guid requestId);
        Task NotifyRequestChangesRequested(Guid requestId);
        Task NotifyCommentResolved(Guid requestId, Guid commentId);
        Task NotifyRequestCountsChanged();
    }

    public class CosigningStateService : ICosigningStateService
    {
        public event Action<CosigningRequest>? RequestUpdated;
        public event Action<Guid>? RequestApproved;
        public event Action<Guid>? RequestChangesRequested;
        public event Action<Guid, Guid>? CommentResolved;
        public event Action? RequestCountsChanged;

        public async Task NotifyRequestUpdated(CosigningRequest request)
        {
            await Task.Run(() => RequestUpdated?.Invoke(request));
        }

        public async Task NotifyRequestApproved(Guid requestId)
        {
            await Task.Run(() => 
            {
                RequestApproved?.Invoke(requestId);
                RequestCountsChanged?.Invoke();
            });
        }

        public async Task NotifyRequestChangesRequested(Guid requestId)
        {
            await Task.Run(() => 
            {
                RequestChangesRequested?.Invoke(requestId);
                RequestCountsChanged?.Invoke();
            });
        }

        public async Task NotifyCommentResolved(Guid requestId, Guid commentId)
        {
            await Task.Run(() => CommentResolved?.Invoke(requestId, commentId));
        }

        public async Task NotifyRequestCountsChanged()
        {
            await Task.Run(() => RequestCountsChanged?.Invoke());
        }
    }
}
