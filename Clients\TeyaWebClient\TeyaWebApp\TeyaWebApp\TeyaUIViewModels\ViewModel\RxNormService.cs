﻿using BusinessLayer.Services;
using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;
using static System.Net.WebRequestMethods;
namespace TeyaUIViewModels.ViewModel
{
    /// <summary>
    /// Uses Prescribable RxNorm API
    /// Can View All Api in : https://lhncbc.nlm.nih.gov/RxNav/APIs/PrescribableAPIs.html
    /// </summary>
    public class RxNormService : IRxNormService
    {
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _RxNormUrl;
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _EncounterNotes;
        private Dictionary<string, string> _cachedDrugNames;
        public RxNormService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _RxNormUrl = Environment.GetEnvironmentVariable("RxNormUrl");
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
            _cachedDrugNames = new Dictionary<string, string>();
        }

        public async Task<List<RxNormConcept>> GetAllRxNormMedications()
        {
            var apiUrl = $"{_EncounterNotes}/api/FDBDrugs/RxNormConcept";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<RxNormConcept>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        public async Task<List<RxNormConcept>> GetAllRxNormMedicationsBySearchTerm(string searchterm)
        {
            var apiUrl = $"{_EncounterNotes}/api/FDBDrugs/RxNormConcept/{searchterm}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<RxNormConcept>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        public async Task<List<RxNormConcept>> GetRxNormSBDCMedications(string str)
        {
            var apiUrl = $"{_EncounterNotes}/api/FDBDrugs/RxNormConceptSBDC/{str}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<RxNormConcept>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Get All Brand Names from RxNorm
        /// </summary>
        public async Task<List<string>> GetAllBrandNames()
        {
            try
            {
                var apiUrl = $"{_RxNormUrl}/allconcepts.json?tty=BN";
                HttpResponseMessage response = await _httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var jsonString = await response.Content.ReadAsStringAsync();

                using (JsonDocument doc = JsonDocument.Parse(jsonString))
                {
                    var brandNames = new List<string>();

                    // Navigate to "minConceptGroup" -> "minConcept" array
                    if (doc.RootElement.TryGetProperty("minConceptGroup", out JsonElement minConceptGroup) &&
                        minConceptGroup.TryGetProperty("minConcept", out JsonElement minConceptArray))
                    {
                        // Iterate through each object and extract the "name"
                        foreach (JsonElement concept in minConceptArray.EnumerateArray())
                        {
                            if (concept.TryGetProperty("name", out JsonElement nameElement))
                            {
                                brandNames.Add(nameElement.GetString());
                            }
                        }
                    }

                    return brandNames;
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Request error: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Unexpected error: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get All Combinations of specific Brand Name in RxNorm
        /// </summary>

        public async Task<List<string>> GetSBDNamesAsync(string name)
        {
            var apiUrl = $"{_RxNormUrl}/drugs.json?name={name}";
            try
            {
                HttpResponseMessage response = await _httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var jsonString = await response.Content.ReadAsStringAsync();
                using (JsonDocument doc = JsonDocument.Parse(jsonString))
                {
                    var sbdNames = new List<string>();

                    // Navigate to "drugGroup" -> "conceptGroup"
                    if (doc.RootElement.TryGetProperty("drugGroup", out JsonElement drugGroup) &&
                        drugGroup.TryGetProperty("conceptGroup", out JsonElement conceptGroupArray))
                    {
                        foreach (JsonElement conceptGroup in conceptGroupArray.EnumerateArray())
                        {
                            // Filter for tty = "SBD"
                            if (conceptGroup.TryGetProperty("tty", out JsonElement ttyElement) &&
                                ttyElement.GetString() == "SBD")
                            {
                                // Extract names from "conceptProperties"
                                if (conceptGroup.TryGetProperty("conceptProperties", out JsonElement conceptPropertiesArray))
                                {
                                    foreach (JsonElement concept in conceptPropertiesArray.EnumerateArray())
                                    {
                                        if (concept.TryGetProperty("name", out JsonElement nameElement))
                                        {
                                            sbdNames.Add(nameElement.GetString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    return sbdNames;
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Request error: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Unexpected error: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get All drug names from RxNorm with their RxCui Id
        /// </summary>
        public async Task<List<string>> GetAllDrugNames()
        {
            List<string> drugNamesList = new List<string>();

            if (_cachedDrugNames.Count > 0)
            {
                drugNamesList = new List<string>(_cachedDrugNames.Values);
            }
            else
            {
                try
                {
                    var apiUrl = $"{_RxNormUrl}/allconcepts.json?tty=IN";
                    HttpResponseMessage response = await _httpClient.GetAsync(apiUrl);
                    response.EnsureSuccessStatusCode();

                    var jsonString = await response.Content.ReadAsStringAsync();

                    using (JsonDocument doc = JsonDocument.Parse(jsonString))
                    {
                        var drugNames = new Dictionary<string, string>();

                        if (doc.RootElement.TryGetProperty("minConceptGroup", out JsonElement minConceptGroup) &&
                            minConceptGroup.TryGetProperty("minConcept", out JsonElement minConceptArray))
                        {
                            foreach (JsonElement concept in minConceptArray.EnumerateArray())
                            {
                                if (concept.TryGetProperty("rxcui", out JsonElement rxcuiElement) &&
                                   concept.TryGetProperty("name", out JsonElement nameElement))
                                {
                                    drugNames[rxcuiElement.GetString()] = nameElement.GetString();
                                }
                            }
                        }
                        _cachedDrugNames = drugNames;
                        drugNamesList = new List<string>(drugNames.Values);
                    }
                }
                catch (HttpRequestException ex)
                {
                    throw new Exception($"Request error: {ex.Message}", ex);
                }
                catch (Exception ex)
                {
                    throw new Exception(_localizer["RxNormError1"]);
                }
            }

            return drugNamesList;
        }

        /// <summary>
        /// Get selected drug RxCui Id
        /// </summary>
        public string GetRxcuiByName(string name)
        {
            foreach (var kvp in _cachedDrugNames)
            {
                if (kvp.Value.Equals(name, StringComparison.OrdinalIgnoreCase))
                {
                    return kvp.Key;
                }
            }
            return null;
        }
    }
}
