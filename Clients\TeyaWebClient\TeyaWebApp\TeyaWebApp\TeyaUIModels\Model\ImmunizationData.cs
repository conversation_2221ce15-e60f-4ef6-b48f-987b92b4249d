﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;

namespace TeyaUIModels.Model
{
    public class ImmunizationData : IModel
    {
        public bool Subscription { get; set; }
        public Guid ImmunizationId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public string Immunizations { get; set; }
        public string CPTCode { get; set; }
        public string CVXCode { get; set; }
        public string? Comments { get; set; }
        public string CPTDescription { get; set; }
        public DateTime? GivenDate { get; set; }
        public Guid PCPId { get; set; }
        public bool IsActive { get; set; }
    }
}
