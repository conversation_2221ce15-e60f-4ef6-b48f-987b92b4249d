﻿using System.Text.Json.Serialization;

namespace TeyaUIModels.Model
{
    public class Product : IModel
    {
        [JsonPropertyName("Id")]
        public Guid Id { get; set; }

        [JsonPropertyName("Name")]
        public string? Name { get; set; }

        [JsonPropertyName("Description")]
        public string? Description { get; set; }

        [JsonPropertyName("Byproduct")]
        public string? Byproduct { get; set; }
        public bool Subscription { get; set; }
        public Guid OrganizationId { get; set; }
    }
}
