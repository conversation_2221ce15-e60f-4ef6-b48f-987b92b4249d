using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IUserThemeService
    {
        Task<IEnumerable<UserTheme>> GetUserThemesAsync(Guid orgId, bool subscription);
        Task<UserTheme> GetUserThemeByIdAsync(Guid id,Guid orgId, bool subscription);
        Task AddUserThemeAsync(UserTheme userTheme);
        Task UpdateUserThemeAsync(UserTheme userTheme);
        Task DeleteUserThemeByIdAsync(Guid id, Guid orgId, bool subscription);
    }
}
