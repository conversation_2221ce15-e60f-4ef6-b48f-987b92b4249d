﻿using System.Text.Json.Serialization;

namespace TeyaUIModels.Model
{
    public class ProductLicense : IModel
    {
        [JsonPropertyName("Id")]
        public Guid Id { get; set; }

        [JsonPropertyName("ProductName")]
        public string? ProductName { get; set; }

        [JsonPropertyName("Description")]
        public string? Description { get; set; }

        [JsonPropertyName("IsLicenseActivated")]
        public bool? IsLicenseActivated { get; set; }
        public Guid OrganizationID { get; set; }
        public bool Subscription { get; set; }

    }
}
