﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFamilyMemberService
    {
        Task CreateMemberAsync(List<FamilyMember> Tasks, Guid? OrgID, bool Subscription);
        Task<List<FamilyMember>> GetMemberAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateFamilyMemberList(List<FamilyMember> familyMembers, Guid? OrgID, bool Subscription);
        Task<List<FamilyMember>> GetFamilyMemberByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task DeleteMemberAsync(Guid taskId, Guid? OrgID, bool Subscription);
        Task UpdateMemberAsync(FamilyMember familyMember, Guid? OrgID, bool Subscription);
    }
}
