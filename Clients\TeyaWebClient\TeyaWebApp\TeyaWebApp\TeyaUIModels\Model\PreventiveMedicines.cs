﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PreventiveMedicines
    {
        public Guid PreventiveMedicineId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public bool IsActive { get; set; }

        //Other Info to show in the grid 
        public string Category { get; set; }
        public string SubCategory { get; set; }
        public string Symptoms { get; set; }
        public string? Detection { get; set; }
        public string? Notes { get; set; }

        public Guid SelectedCategoryId { get; set; }
        public Guid SelectedSubCategoryId { get; set; }
        public Guid SelectedSymptomId { get; set; }

    }
}