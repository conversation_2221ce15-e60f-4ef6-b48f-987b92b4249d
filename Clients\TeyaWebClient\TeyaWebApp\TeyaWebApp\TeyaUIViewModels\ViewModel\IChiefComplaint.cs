﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IChiefComplaintService
    {
        Task<List<ChiefComplaintDTO>> GetAllComplaintsAsync(Guid? OrgID, bool Subscription);
        Task AddAsync(ChiefComplaintDTO complaintDto, Guid? OrgID, bool Subscription);
        Task UpdateComplaintAsync(Guid id, ChiefComplaintDTO complaintDto, Guid? OrgID, bool Subscription);
        Task DeleteComplaintAsync(Guid id, Guid? OrgID, bool Subscription);

        Task UpdateComplaintListAsync(List<ChiefComplaintDTO> complaints, Guid? OrgID, bool Subscription);
        Task<IEnumerable<ChiefComplaintDTO>> GetByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task AddAsync(List<ChiefComplaintDTO> complaintList, Guid? OrgID, bool Subscription);
        Task<List<ChiefComplaintDTO>> LoadComplaintsAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task<List<ChiefComplaintDTO>> GetProcessedComplaintsAsync(Guid? OrgID, bool Subscription);
    }
}
