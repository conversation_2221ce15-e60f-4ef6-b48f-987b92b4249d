﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class ProgressNotesService : IProgressNotesService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public ProgressNotesService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL") ?? configuration["EncounterNotesURL"];
            _tokenService = tokenService;
        }


        public async Task<Record> GetRecordByIdAsync(Guid recordId, Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Records/{recordId}/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching record: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<Record>(responseData, options);
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the record: {ex.Message}", ex);
            }
        }
        public async Task<List<Record>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Records/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching record: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<Record>>(responseData, options) ?? new List<Record>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the record: {ex.Message}", ex);
            }
        }


        public async Task<List<Record>> GetRecordsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/PatientId/{id}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching records: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<Record>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<Record>>(responseData, options) ?? new List<Record>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving records: {ex.Message}", ex);
            }
        }

        public async Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/ByPCP/{pcpId}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching records: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<Record>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<Record>>(responseData, options) ?? new List<Record>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving records: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Records/{updatedRecord.Id}/{OrgID}/{Subscription}";

                var serializableRecord = new Record
                {
                    Id = updatedRecord.Id,
                    PatientId = updatedRecord.PatientId,
                    OrganizationId = updatedRecord.OrganizationId,
                    PatientName = updatedRecord.PatientName,
                    PCPId = updatedRecord.PCPId,
                    DateTime = updatedRecord.DateTime,
                    Notes = updatedRecord.Notes,
                    isEditable = updatedRecord.isEditable,
                    Transcription = updatedRecord.Transcription,
                    Template = updatedRecord.Template
                };

                var bodyContent = JsonSerializer.Serialize(serializableRecord, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating record: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<HttpResponseMessage> UploadRecordAsync(Record updatedRecord,Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Records/EncounterNote/{OrgID}/{Subscription}";

                var bodyContent = JsonSerializer.Serialize(new List<Record> { updatedRecord }, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error response: {response.StatusCode}, Content: {errorContent}");
                }
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating record: {ex.Message}", ex);
                throw;
            }
        }
    }
}

