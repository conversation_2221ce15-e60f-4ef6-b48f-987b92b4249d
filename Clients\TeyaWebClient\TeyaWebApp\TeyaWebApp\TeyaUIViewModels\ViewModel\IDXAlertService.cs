﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDXAlertService
    {
        Task<List<DiagnosisAlert>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<DiagnosisAlert>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddDXAlertsAsync(List<DiagnosisAlert> dxAlerts, Guid? OrgID, bool Subscription);
        Task UpdateDXAlertAsync(DiagnosisAlert dxAlert, Guid? OrgID, bool Subscription);
        Task UpdateDXAlertsListAsync(List<DiagnosisAlert> dxAlerts, Guid? OrgID, bool Subscription);
        Task DeleteDXAlertByEntityAsync(DiagnosisAlert dxAlert, Guid? OrgID, bool Subscription);
    }
}