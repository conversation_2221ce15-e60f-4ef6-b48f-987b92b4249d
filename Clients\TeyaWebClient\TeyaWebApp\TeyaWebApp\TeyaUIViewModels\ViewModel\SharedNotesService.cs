﻿using System;
using System.Collections.Generic;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class SharedNotesService
    {
        private List<ChiefComplaintDTO> chiefComplaints = new();

        public event Action OnChange;  

        public List<ChiefComplaintDTO> GetChiefComplaints() => new List<ChiefComplaintDTO>(chiefComplaints);  

        public void AddChiefComplaints(List<ChiefComplaintDTO> newComplaints)
        {
            if (newComplaints != null && newComplaints.Count > 0)
            {
                chiefComplaints.AddRange(newComplaints);
                NotifyStateChanged();  
            }
        }
        public void AssessmentsChanged() => NotifyStateChanged(); 

        private void NotifyStateChanged() => OnChange?.Invoke();  
    }
}
