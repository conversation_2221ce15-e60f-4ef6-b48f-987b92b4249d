﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PatientSocialHistory : IModel
    {
        public bool Subscription { get; set; }
        public Guid SocialHistoryId { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId{ get; set; }
        public Guid PCPId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? Occupation { get; set; }
        public string? LivingSituation { get; set; }
        public string? MaritalStatus { get; set; }
        public string? LifestyleHabits { get; set; }
        public string? Educationlevel { get; set; }
        public bool isActive { get; set; }
    }
}
