﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IGynHistoryService
    {
        Task AddAsync(GynHistoryDTO gynHistoryDto, Guid? OrgID, bool Subscription);
        Task UpdateGynHistoryAsync(Guid id, GynHistoryDTO gynHistoryDto, Guid? OrgID, bool Subscription);
        Task DeleteGynHistoryAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateGynHistoryListAsync(List<GynHistoryDTO> gynHistories, Guid? OrgID, bool Subscription);
        Task<List<GynHistoryDTO>> LoadGynHistoriesAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task<IEnumerable<GynHistoryDTO>> GetByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription);
    }
}
