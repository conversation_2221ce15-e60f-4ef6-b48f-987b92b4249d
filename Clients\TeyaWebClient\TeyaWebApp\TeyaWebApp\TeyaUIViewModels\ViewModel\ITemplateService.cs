﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ITemplateService
    {
        Task<List<TemplateData>> GetTemplatesAsync(Guid? OrgID, bool Subscription);
        Task<List<TemplateData>> GetTemplatesByIdAsync(Guid Id, Guid? OrgID, bool Subscription);
        Task CreateTemplatesAsync(TemplateData templates, Guid? OrgID, bool Subscription);
        Task DeleteTemplatesAsync(Guid templateId, Guid? OrgID, bool Subscription);
        Task<List<TemplateData>> GetTemplatesByPCPIdAsync(Guid PCPId, Guid? OrgID, bool Subscription);
        Task UpdateTemplatesAsync(List<TemplateData> templates, Guid? OrgID, bool Subscription);
    }
}