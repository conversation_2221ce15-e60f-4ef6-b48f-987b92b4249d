﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPracticeService
    {
        Task CreateTasksAsync(List<Tasks> Tasks, Guid OrgID, bool Subscription);
        Task<List<Tasks>> GetTasksAsync();
        Task<List<ProviderPatient>> GetMemberAsync();
        Task DeleteTasksAsync(Guid taskId, Guid OrgID, bool Subscription);
        Task UpdateTasksByPatientIdAsync(Tasks task, Guid OrgID, bool Subscription);
        Task UpdateTasksAsync(Tasks task, Guid OrgID, bool Subscription);
    }
}
