﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMedicalHistoryService
    {
        Task<List<MedicalHistoryDTO>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<MedicalHistoryDTO>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddMedicalHistoryAsync(List<MedicalHistoryDTO> medicalHistories, Guid? OrgID, bool Subscription);
        Task UpdateMedicalHistoryAsync(MedicalHistoryDTO medicalHistory, Guid? OrgID, bool Subscription);
        Task UpdateMedicalHistoryListAsync(List<MedicalHistoryDTO> medicalHistories, Guid? OrgID, bool Subscription);
        Task DeleteMedicalHistoryByEntityAsync(MedicalHistoryDTO medicalHistory, Guid? OrgID, bool Subscription);
    }
}
