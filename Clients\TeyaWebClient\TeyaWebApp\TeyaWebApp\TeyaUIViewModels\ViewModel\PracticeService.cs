﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PracticeService : IPracticeService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _Practice;
        private readonly string _ProviderPatientUrl;
        private readonly ITokenService _tokenService;
        private readonly ILogger<PracticeService> _logger;

        public PracticeService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService, ILogger<PracticeService> logger)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            Env.Load();
            _Practice = Environment.GetEnvironmentVariable("PracticeApiURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get All Tasks
        /// </summary>
        public async Task<List<Tasks>> GetTasksAsync()
        {
            List<Tasks> tasks = new(); 
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_Practice}/api/Practice";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    tasks = await response.Content.ReadFromJsonAsync<List<Tasks>>() ?? new List<Tasks>();
                }
                else
                {
                    var errorMessage = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"{_localizer["TasksRetrievalFailure"]}: {errorMessage}");
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError($"Error fetching tasks: {ex.Message}");
                throw;
            }
            return tasks; 
        }

        /// <summary>
        ///  Create A List Of Tasks
        /// </summary>
        public async Task CreateTasksAsync(List<Tasks> Tasks, Guid OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Tasks);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var apiUrl = $"{_Practice}/api/Practice/registration/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Update a single Tasks
        /// </summary>
        public async Task UpdateTasksAsync(Tasks Tasks, Guid OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_Practice}/api/Practice/{Tasks.Id}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Tasks);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task DeleteTasksAsync(Guid TasksId, Guid OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_Practice}/api/Practice/{TasksId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }

        /// <summary>
        ///  Update a single Tasks with PatientId
        /// </summary>
        public async Task UpdateTasksByPatientIdAsync(Tasks Tasks, Guid OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_Practice}/api/Practice/Patient/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Tasks);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Delete a Tasks using TasksId
        /// </summary>
        public async Task<List<ProviderPatient>> GetMemberAsync()
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_Practice}/api/ProviderPatient";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ProviderPatient>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }
    }
}