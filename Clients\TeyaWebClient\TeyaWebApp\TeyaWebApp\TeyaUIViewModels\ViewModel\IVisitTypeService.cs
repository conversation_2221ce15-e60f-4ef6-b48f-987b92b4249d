﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModels
{
    public interface IVisitTypeService
    {
        Task<List<VisitType>> GetAllVisitTypesAsync();
        Task<List<string>> GetVisitTypeNamesAsync(Guid? orgId);
        Task<List<VisitType>> GetVisitTypesByOrganizationIdAsync(Guid orgId, bool Subscription);
        Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode, bool Subscription);
        Task<bool> AddVisitTypeAsync(VisitType visitType);
        Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName, bool Subscription);
    }
}
