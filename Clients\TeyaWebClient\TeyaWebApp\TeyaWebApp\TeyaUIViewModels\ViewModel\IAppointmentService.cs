﻿
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IAppointmentService
    {
        Task<List<Appointment>> GetAppointmentsAsync(DateTime date, Guid? OrgID, bool Subscription);
        Task CreateAppointmentsAsync(List<Appointment> appointments);
        Task DeleteAppointmentAsync(Guid appointmentId, Guid? OrgID, bool Subscription);
        Task UpdateAppointmentAsync(Appointment appointment);
        Task<List<Appointment>> GetAppointmentsByIdAsync(Guid id);
    }

}
