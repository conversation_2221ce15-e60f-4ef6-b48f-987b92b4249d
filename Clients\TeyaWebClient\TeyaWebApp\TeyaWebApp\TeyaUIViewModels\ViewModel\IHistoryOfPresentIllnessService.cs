﻿using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IHistoryOfPresentIllnessService
    {
        Task<List<HistoryOfPresentIllness>> GetHpiByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task<List<HistoryOfPresentIllness>> GetActiveHpiByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task AddHpiAsync(List<HistoryOfPresentIllness> hpiRecords, Guid? OrgID, bool Subscription);
        Task UpdateHpiListAsync(List<HistoryOfPresentIllness> hpiRecords, Guid? OrgID, bool Subscription);
    }
}
