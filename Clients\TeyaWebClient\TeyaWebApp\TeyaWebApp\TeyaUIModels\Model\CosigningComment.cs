using System;

namespace TeyaUIModels.Model
{
    public class CosigningComment : IModel
    {
        public Guid Id { get; set; }
        public string Comment { get; set; }
        public Guid CommenterId { get; set; }
        public string CommenterName { get; set; }
        public DateTime CommentDate { get; set; }
        public bool IsResolved { get; set; } = false;
        public DateTime? ResolvedDate { get; set; }
        public Guid? ResolvedById { get; set; }
        public string ResolvedByName { get; set; }
        public string SelectedText { get; set; } // For highlighting specific parts of notes
        public int? StartPosition { get; set; } // Position in the notes where comment applies
        public int? EndPosition { get; set; } // End position for text selection
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;
    }
}
