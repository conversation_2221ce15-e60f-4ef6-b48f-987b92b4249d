﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDiagnosticImagingPageService
    {
        Task CreateDiagnosticImagingAsync(List<DiagnosticImagingDTO> Tasks, Guid? OrgID, bool Subscription);
        Task CreateAssessmentAsync(List<DiagnosticImagingAssessment> Tasks, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImaging, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImagingAssessment>> GetAssessmentyById(Guid id, Guid? OrgID, bool Subscription);
        Task DeleteDiagnosticImagingAsync(Guid taskId, Guid? OrgID, bool Subscription);
        Task UpdateDiagnosticImagingAsync(DiagnosticImagingDTO diagnosticImaging, Guid? OrgID, bool Subscription);
    }
}
