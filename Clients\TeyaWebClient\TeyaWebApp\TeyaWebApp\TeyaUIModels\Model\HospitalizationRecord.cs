﻿using System;
using System.Text.Json.Serialization;
using TeyaUIModels.Model;

namespace TeyaUIModels.Model
{
    public class HospitalizationRecord : IModel
    {
        public Guid RecordID { get; set; }
        public string Reason { get; set; }
        public DateTime? Date { get; set; }
        public Guid? OrganizationID { get; set; }
        public Guid PatientId { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool Subscription { get; set; }
    }
}