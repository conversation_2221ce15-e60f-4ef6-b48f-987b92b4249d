﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPhysicalService
    {
        Task<List<Physicalexamination>> GetExaminationsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddExaminationAsync(List<Physicalexamination> examinations, Guid? OrgID, bool Subscription);
        Task DeleteExaminationAsync(Physicalexamination physical, Guid? OrgID, bool Subscription);
        Task UpdateExaminationAsync(Physicalexamination examination, Guid? OrgID, bool Subscription);
        Task<List<Physicalexamination>> GetExaminationByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateExaminationListAsync(List<Physicalexamination> physicals, Guid? OrgID, bool Subscription);
    }
}
