﻿using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMemberService
    {
        Task<Member> RegisterMembersAsync(List<Member> members);
        Task<List<Member>> GetMembersForProductAsync(Guid productId, Guid OrgID, bool Subscription);
        Task DeleteMemberByIdAsync(Guid id, Guid OrgID, bool Subscription);
        Task UpdateMemberByIdAsync(Guid memberId, Member member);
        Task<List<string>> GetProviderlistAsync(Guid orgId, bool Subscription);
        Task<List<Member>> GetAllMembersAsync(Guid OrgID, bool Subscription);
        Task<Member> GetMemberByIdAsync(Guid memberId, Guid OrgID, bool Subscription);
        Task<List<Member>> SearchMembersAsync(string query, Guid OrgID, bool Subscription);
        Task<string> RegisterMembersContentAsync(List<Member> members);
        Task<List<ProviderPatient>> GetProviderPatientByOrganizationIdAsync(Guid OrganizationId, bool Subscription);
        Task<List<ProviderPatient>> GetPatientsByOrganizationIdAsync(Guid Organization, bool Subscription);
        Task<bool> HasProductAccess(Guid memberId, Guid productId, Guid OrgID, bool Subscription);
        Task<bool> SearchMembersEmailAsync(string query, Guid OrgID, bool Subscription);
    }
}
