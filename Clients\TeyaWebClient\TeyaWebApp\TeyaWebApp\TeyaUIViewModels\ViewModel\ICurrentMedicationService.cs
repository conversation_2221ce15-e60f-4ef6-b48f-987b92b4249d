﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICurrentMedicationService
    {
        Task<List<ActiveMedication>> GetMedicationsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<ActiveMedication>> GetMedicationsByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task AddMedicationAsync(List<ActiveMedication> medicines, Guid? OrgID, bool Subscription);
        Task DeletemedicationAsync(ActiveMedication medicine, Guid? OrgID, bool Subscription);
        Task UpdateMedicationAsync(ActiveMedication medicines, Guid? OrgID, bool Subscription);
        Task UpdateMedicationListAsync(List<ActiveMedication> medicines, Guid? OrgID, bool Subscription);
    }
}
