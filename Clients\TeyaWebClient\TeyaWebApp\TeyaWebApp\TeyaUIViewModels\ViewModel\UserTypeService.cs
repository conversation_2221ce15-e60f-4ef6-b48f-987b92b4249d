﻿using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using TeyaWebApp.Model;

namespace TeyaWebApp.ViewModel
{
    public class UserTypeService : IUserTypeService
    {
        private readonly HttpClient _httpClient;

        public UserTypeService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<UserType> GetUserTypeByIdAsync(Guid id)
        {
            var response = await _httpClient.GetAsync($"api/UserType/{id}");
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<UserType>();
            }
            throw new Exception($"Error retrieving UserType with ID {id}: {response.ReasonPhrase}");
        }

        public async Task AddUserTypeAsync(UserType userType)
        {
            var response = await _httpClient.PostAsJsonAsync("http://localhost/MemberServiceApi/api/UserType", userType);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Error adding UserType: {response.ReasonPhrase}");
            }
        }

        public async Task UpdateUserTypeAsync(Guid id, UserType userType)
        {
            var response = await _httpClient.PutAsJsonAsync($"api/UserType/{id}", userType);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Error updating UserType with ID {id}: {response.ReasonPhrase}");
            }
        }

        public async Task DeleteUserTypeAsync(Guid id)
        {
            var response = await _httpClient.DeleteAsync($"api/UserType/{id}");
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Error deleting UserType with ID {id}: {response.ReasonPhrase}");
            }
        }
    }
}
