﻿using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IProcedureService
    {
        Task<List<Procedures>> GetProcedureByPatientId(Guid patientId, Guid? OrgID, bool Subscription);
        Task AddProcedureAsync(List<Procedures> procedure, Guid? OrgID, bool Subscription);
        Task UpdateProcedureListAsync(List<Procedures> procedure, Guid? OrgID, bool Subscription);
        Task<List<Procedures>> LoadProcedureAsync(Guid patientId, Guid? OrgID, bool Subscription);
    }
}
