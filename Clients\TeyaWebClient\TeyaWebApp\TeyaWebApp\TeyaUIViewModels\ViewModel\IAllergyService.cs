﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IAllergyService
    {
        Task<List<Allergy>> GetAllergyByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<Allergy>> GetAllergyByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task AddAllergyAsync(List<Allergy> Allergy, Guid? OrgID, bool Subscription);
        Task DeleteAllergyAsync(Allergy allergy, Guid? OrgID, bool Subscription);
        Task UpdateAllergyAsync(Allergy Allergy, Guid? OrgID, bool Subscription);
        Task UpdateAllergyListAsync(List<Allergy> newAllergies, List<Allergy> updatedAllergies, List<Allergy> deletedAllergies, Guid patientId, Guid? OrgID, bool Subscription);
    }
}