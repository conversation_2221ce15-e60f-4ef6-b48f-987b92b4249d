﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public  class Measure : IModel
    {
        public Guid Id { get; set; }
        public string MeasureTitle { get; set; }
        public string? MeasureNumerator { get; set; }
        public string? MeasureDenominator { get; set; }
        public string MeasureDescription { get; set; }
        public string? CDSS { get; set; }
        public string? MeasureType { get; set; }
        public string? OrderSetLinked { get; set; }
        public bool IsDeleted { get; set; }
    }
}
