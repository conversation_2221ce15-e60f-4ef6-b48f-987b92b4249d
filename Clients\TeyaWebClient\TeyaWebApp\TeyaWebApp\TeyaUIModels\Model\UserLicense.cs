﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class UserLicense : IModel
    {
        public Guid Id { get; set; }
        public Guid PlanId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid? ProductId { get; set; }
        public int Seats { get; set; }
        public int ActiveUsers { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public bool Status { get; set; } = true;
        public DateTime ExpiryDate { get; set; }
    }
}

