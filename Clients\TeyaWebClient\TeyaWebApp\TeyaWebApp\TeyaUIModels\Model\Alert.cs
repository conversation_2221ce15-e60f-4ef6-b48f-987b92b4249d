using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Alert
    {
        public Guid AlertId { get; set; }
        public Guid? OrdersetId { get; set; }
        public string PatientName { get; set; }
        public string Severity { get; set; }
        public string AlertType { get; set; }
        public string Description { get; set; }
        public string Solution { get; set; }
        public string AdditionalInfo { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
    }
}
