﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class OrderSet
    {
        public Guid Id { get; set; }
        public string? OrderSetName { get; set; }
        public string? Diagnosis { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool isActive { get; set; }
        public bool Subscription { get; set; }
    }
}
