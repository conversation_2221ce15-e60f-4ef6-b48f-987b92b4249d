﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IGuardianService
    {
        Task<Guardian?> GetGuardianByIdAsync(Guid id, Guid OrgID, bool Subscription);

        Task<bool> AddG<PERSON>ianAsync(Guardian Guardian);

        Task<bool> UpdateGuardianAsync(Guid id, Guardian guardian);

        Task<bool> DeleteGuardianAsync(Guid id, Guid OrgID, bool Subscription);

        Task<List<Guardian>?> GetGuardianByNameAsync(string name);

        Task<List<Guardian>?> GetAllGuardianAsync(Guid orgId, bool Subscription);
    }
}
