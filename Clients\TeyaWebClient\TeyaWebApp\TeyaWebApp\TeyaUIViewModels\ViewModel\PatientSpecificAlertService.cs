﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using DotNetEnv;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class PatientSpecificAlertsService : IPatientSpecificAlertsService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _alertsServiceUrl;
        private readonly IStringLocalizer<PatientSpecificAlertsService> _localizer;
        private readonly ILogger<PatientSpecificAlertsService> _logger;

        public PatientSpecificAlertsService(
            HttpClient httpClient,
            IStringLocalizer<PatientSpecificAlertsService> localizer,
            ILogger<PatientSpecificAlertsService> logger,
            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _tokenService = tokenService;

            Env.Load();
            _alertsServiceUrl = Environment.GetEnvironmentVariable("AlertsServiceURL");
        }

        /// <summary>
        /// Gets all patient specific alerts for a patient
        /// </summary>
        /// <param name="patientId">The patient ID</param>
        /// <returns>List of all patient specific alerts for the patient</returns>
        public async Task<IEnumerable<PatientSpecificAlertsData>> GetAllPatientSpecificAlertsAsync(Guid patientId)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts/{patientId}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PatientSpecificAlertsData>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPatientSpecificAlerts"], patientId);
                throw;
            }
        }

        /// <summary>
        /// Gets all active patient specific alerts for a patient
        /// </summary>
        /// <param name="patientId">The patient ID</param>
        /// <returns>List of active patient specific alerts for the patient</returns>
        public async Task<IEnumerable<PatientSpecificAlertsData>> GetActivePatientSpecificAlertsAsync(Guid patientId)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts/{patientId}/active";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<PatientSpecificAlertsData>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingActivePatientSpecificAlerts"], patientId);
                throw;
            }
        }

        /// <summary>
        /// Adds new patient specific alerts
        /// </summary>
        /// <param name="alerts">The patient specific alerts to add</param>
        /// <returns>Success message if alerts are added successfully</returns>
        public async Task AddPatientSpecificAlertsAsync(List<PatientSpecificAlertsData> alerts)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts";

                var bodyContent = JsonSerializer.Serialize(alerts);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PatientSpecificAlertsAddedSuccessfully"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddPatientSpecificAlertsFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingPatientSpecificAlerts"]);
                throw;
            }
        }

        /// <summary>
        /// Adds a single patient specific alert
        /// </summary>
        /// <param name="alert">The patient specific alert to add</param>
        /// <returns>Success message if alert is added successfully</returns>
        public async Task AddPatientSpecificAlertAsync(PatientSpecificAlertsData alert)
        {
            await AddPatientSpecificAlertsAsync(new List<PatientSpecificAlertsData> { alert });
        }

        /// <summary>
        /// Updates an existing patient specific alert
        /// </summary>
        /// <param name="alert">The patient specific alert to update</param>
        /// <returns>Success message if alert is updated successfully</returns>
        public async Task UpdatePatientSpecificAlertAsync(PatientSpecificAlertsData alert)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts/{alert.Id}";

                var bodyContent = JsonSerializer.Serialize(alert);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PatientSpecificAlertUpdatedSuccessfully"], alert.Id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PatientSpecificAlertUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPatientSpecificAlert"], alert.Id);
                throw;
            }
        }

        /// <summary>
        /// Updates a list of patient specific alerts
        /// </summary>
        /// <param name="alerts">The list of patient specific alerts to update</param>
        /// <returns>Success message if alerts are updated successfully</returns>
        public async Task UpdatePatientSpecificAlertsListAsync(List<PatientSpecificAlertsData> alerts)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts";

                var bodyContent = JsonSerializer.Serialize(alerts);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PatientSpecificAlertsListUpdatedSuccessfully"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update list failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PatientSpecificAlertsListUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingPatientSpecificAlertsList"]);
                throw;
            }
        }

        /// <summary>
        /// Deletes a patient specific alert by ID
        /// </summary>
        /// <param name="id">The ID of the patient specific alert to delete</param>
        /// <returns>Success message if alert is deleted successfully</returns>
        public async Task DeletePatientSpecificAlertByIdAsync(Guid id)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts/{id}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PatientSpecificAlertDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PatientSpecificAlertDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPatientSpecificAlert"], id);
                throw;
            }
        }

        /// <summary>
        /// Deletes a patient specific alert by entity
        /// </summary>
        /// <param name="alert">The patient specific alert to delete</param>
        /// <returns>Success message if alert is deleted successfully</returns>
        public async Task DeletePatientSpecificAlertByEntityAsync(PatientSpecificAlertsData alert)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_alertsServiceUrl}/api/PatientSpecificAlerts";

                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
                {
                    Content = JsonContent.Create(alert)
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["PatientSpecificAlertDeletedSuccessfully"], alert.Id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete by entity failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["PatientSpecificAlertDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPatientSpecificAlert"], alert.Id);
                throw;
            }
        }
    }
}