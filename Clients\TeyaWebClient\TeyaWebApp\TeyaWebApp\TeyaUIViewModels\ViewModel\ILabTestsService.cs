﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ILabTestsService
    {
        Task<List<LabTests>> GetAllByIdAsync(Guid id);
        Task<List<LabTests>> GetAllByIdAndIsActiveAsync(Guid id);
        Task AddLabTestsAsync(List<LabTests> labTests);
        Task UpdateLabTestsAsync(LabTests labTest);
        Task UpdateLabTestsListAsync(List<LabTests> labTests);
        Task DeleteLabTestsByEntityAsync(LabTests labTest);
    }
}
