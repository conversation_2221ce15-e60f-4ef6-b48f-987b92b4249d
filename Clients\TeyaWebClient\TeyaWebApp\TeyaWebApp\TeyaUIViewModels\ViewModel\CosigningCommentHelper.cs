using System;
using System.Collections.Generic;
using System.Text.Json;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningCommentHelper
    {
        List<CosigningComment> GetComments(string commentsJson);
        string SetComments(List<CosigningComment> comments);
    }

    public class CosigningCommentHelper : ICosigningCommentHelper
    {
        public List<CosigningComment> GetComments(string commentsJson)
        {
            if (string.IsNullOrEmpty(commentsJson) || commentsJson == "[]")
                return new List<CosigningComment>();

            try
            {
                return JsonSerializer.Deserialize<List<CosigningComment>>(commentsJson) ?? new List<CosigningComment>();
            }
            catch
            {
                return new List<CosigningComment>();
            }
        }

        public string SetComments(List<CosigningComment> comments)
        {
            return JsonSerializer.Serialize(comments ?? new List<CosigningComment>());
        }
    }
}
