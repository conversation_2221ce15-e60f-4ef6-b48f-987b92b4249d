﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class OrderSetDiagnosticImaging
    {
        public Guid RecordID { get; set; }
        public string DiCompany { get; set; }
        public string? ccResults { get; set; }
        public string Type { get; set; }
        public string Lookup { get; set; }
        public string OrderName { get; set; }
        public string StartsWith { get; set; }
        public Guid OrderSetId { get; set; }
    }
}