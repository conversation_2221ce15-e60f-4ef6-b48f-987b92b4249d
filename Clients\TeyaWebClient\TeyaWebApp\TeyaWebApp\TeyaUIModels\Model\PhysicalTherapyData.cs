﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PhysicalTherapyData : IModel
    {
        public bool Subscription { get; set; }
        public Guid PhysicalTherapyID { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? TherapyAssessment { get; set; }
        public string? ShortTermGoals { get; set; }
        public string? LongTermGoals { get; set; }
        public string? PhysicalTherapyDiagnosis { get; set; }
        public string? PhysicalTherapyProgram { get; set; }
        public string? Disabilities { get; set; }
        public string? Functionals { get; set; }
        public string? Limitations { get; set; }
        public Guid? PCPId { get; set; }
        public bool? IsActive { get; set; }
    }
}
