﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class RedisCacheService : ICacheService
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly IDatabase _cache;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly string _instanceName;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly JsonSerializerOptions _jsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        };

        public RedisCacheService(
            IConnectionMultiplexer redis,
            ILogger<RedisCacheService> logger,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer)
        {
            _redis = redis ?? throw new ArgumentNullException(nameof(redis));
            _cache = redis.GetDatabase();
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _instanceName = configuration["Redis:InstanceName"] ?? "TeyaWebApp:";
        }

        private string GetFullKey(string key) => $"{_instanceName}{key}";

        /// <summary>
        /// Specialized method for FDB medications with 24-hour cache duration
        /// </summary>
        public async Task<List<FDBMedicationName>> GetFDBMedicationsAsync(Func<Task<List<FDBMedicationName>>> fetchFunc)
        {
            const string key = "FDB_Medications";
            var fullKey = GetFullKey(key);

            try
            {
                // 1. Attempt cache retrieval
                var cachedData = await _cache.StringGetAsync(fullKey);
                if (!cachedData.IsNullOrEmpty)
                {
                    _logger.LogDebug("FDB medications cache hit");
                    return JsonSerializer.Deserialize<List<FDBMedicationName>>(cachedData, _jsonOptions);
                }

                // 2. Cache miss - fetch from source
                _logger.LogDebug("FDB medications cache miss");
                var medications = await fetchFunc();

                // 3. Cache the results if valid
                if (medications?.Count > 0)
                {
                    await _cache.StringSetAsync(
                        fullKey,
                        JsonSerializer.Serialize(medications, _jsonOptions),
                        expiry: TimeSpan.FromHours(24),
                        flags: CommandFlags.FireAndForget); // Async cache population
                }

                return medications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve FDB medications from cache");
                return await fetchFunc(); // Graceful fallback
            }
        }

        /// <summary>
        /// Generic cache-aside pattern implementation
        /// </summary>
        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getDataFunc, TimeSpan? expirationTime = null)
        {
            var fullKey = GetFullKey(key);

            try
            {
                // 1. Check cache
                var cachedValue = await _cache.StringGetAsync(fullKey);
                if (!cachedValue.IsNullOrEmpty)
                {
                    _logger.LogDebug("Cache hit for {Key}", key);
                    return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
                }

                // 2. Cache miss - get fresh data
                _logger.LogDebug("Cache miss for {Key}", key);
                var data = await getDataFunc();

                // 3. Update cache if data exists
                if (data != null)
                {
                    await _cache.StringSetAsync(
                        fullKey,
                        JsonSerializer.Serialize(data, _jsonOptions),
                        expirationTime ?? TimeSpan.FromMinutes(30));
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache operation failed for {Key}", key);
                return await getDataFunc(); // Fail gracefully
            }
        }

        /// <summary>
        /// Force refresh all medication-related cache entries
        /// </summary>
        public async Task RefreshMedicationData()
        {
            var medicationKeys = new[]
            {
                "FDB_Medications",
                "Medication_Search_Index",
                "Drug_Interactions_*" // Wildcard pattern
            };

            try
            {
                foreach (var key in medicationKeys)
                {
                    if (key.EndsWith("*"))
                    {
                        await RemoveByPatternAsync(key[..^1]); // Remove wildcard
                    }
                    else
                    {
                        await RemoveAsync(key);
                    }
                }
                _logger.LogInformation("Refreshed all medication cache entries");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh medication cache");
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                var fullKey = GetFullKey(key);
                await _cache.KeyDeleteAsync(fullKey);
                _logger.LogDebug("Removed cache key: {Key}", fullKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove cache key: {Key}", key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                var endpoints = _redis.GetEndPoints();
                var server = _redis.GetServer(endpoints.First());
                var keys = server.Keys(pattern: GetFullKey(pattern) + "*").ToArray();

                if (keys.Length > 0)
                {
                    await _cache.KeyDeleteAsync(keys);
                    _logger.LogDebug("Removed {Count} keys matching {Pattern}", keys.Length, pattern);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Pattern removal failed for {Pattern}", pattern);
            }
        }

        public async Task<bool> KeyExistsAsync(string key)
        {
            try
            {
                var fullKey = GetFullKey(key);
                return await _cache.KeyExistsAsync(fullKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check key existence: {Key}", key);
                return false;
            }
        }
    }
}