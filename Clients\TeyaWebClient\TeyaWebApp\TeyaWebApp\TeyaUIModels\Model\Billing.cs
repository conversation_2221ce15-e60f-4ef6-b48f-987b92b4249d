﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Billing : IModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid BillingId { get; set; }

        [Required]
        public Guid PatientId { get; set; }

        [Required]
        public Guid VisitTypeId { get; set; }

        [Required]
        public Guid AssessmentId { get; set; }

        [Required]
        public Guid ProcedureCodeId { get; set; }

        [Required]
        public Guid EMCodeId { get; set; }

        public string BillingNotes { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    }
}
