﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPreventiveMedicineService
    {
        Task<List<PreventiveMedicines>> GetPreventiveMedicinesByPatientIdAndIsActiveAsync(Guid id, Guid OrgID, bool Subscription);
        Task AddPreventiveMedicineAsync(List<PreventiveMedicines> medicines, Guid OrgID, bool Subscription);
        Task UpdatePreventiveMedicinesInBulk(List<PreventiveMedicines> medicines, Guid OrgID, bool Subscription);
        Task<List<PMCategory>> GetAllPMCategoriesAsync();
        Task AddPMCategoryAsync(List<PMCategory> category);
        Task UpdatePMCategoryAsync(List<PMCategory> category);
         Task<List<PMSubCategory>> GetAllPMSubCategoriesAsync();
        Task<List<PMSubCategory>> GetPMSubCategoriesByPMCategoryIdAsync(Guid id);
        Task AddPMSubCategoryAsync(List<PMSubCategory> category);
        Task UpdatePMSubCategoryAsync(List<PMSubCategory> category);
        Task<List<PMSymptoms>> GetAllPMSymptomsAsync();
        Task<List<PMSymptoms>> GetPMSymptomsBySubCategoryIdAsync(Guid id);
        Task AddPMSymptomsAsync(List<PMSymptoms> symptoms);
        Task UpdatePMSymptoms(List<PMSymptoms> symptoms);


    }
}
