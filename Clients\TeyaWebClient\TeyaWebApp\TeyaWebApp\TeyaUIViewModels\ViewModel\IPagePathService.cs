﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IPagePathService
    {
        Task<IEnumerable<PagePath>> GetPagePathsAsync();
        Task<PagePath> GetPagePathByIdAsync(Guid id);
        Task AddPagePathAsync(PagePath pagePath);
        Task UpdatePagePathAsync(PagePath pagePath);
        Task DeletePagePathByIdAsync(Guid id);
    }
}
