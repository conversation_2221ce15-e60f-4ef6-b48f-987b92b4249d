﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TeyaWebApp.TeyaAIScribeResource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TeyaAIScribeResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TeyaAIScribeResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TeyaWebApp.TeyaAIScribeResource.TeyaAIScribeResource", typeof(TeyaAIScribeResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {productId}.
        /// </summary>
        public static string _productId_ {
            get {
                return ResourceManager.GetString("{productId}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ABOUT.
        /// </summary>
        public static string ABOUT {
            get {
                return ResourceManager.GetString("ABOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accept Assignment.
        /// </summary>
        public static string AcceptAssignment {
            get {
                return ResourceManager.GetString("AcceptAssignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access record not found.
        /// </summary>
        public static string AccessNotFound {
            get {
                return ResourceManager.GetString("AccessNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AccessTokenMissing.
        /// </summary>
        public static string AccessTokenMissing {
            get {
                return ResourceManager.GetString("AccessTokenMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AccessTokenNotFound.
        /// </summary>
        public static string AccessTokenNotFound {
            get {
                return ResourceManager.GetString("AccessTokenNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access updated successfully.
        /// </summary>
        public static string AccessUpdateSuccessful {
            get {
                return ResourceManager.GetString("AccessUpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Congratulations on creating your account with Teya Health! We’re thrilled to have you on board as we revolutionize the healthcare experience using AI technology. Our innovative products, especially for generating SOAP notes, will make your daily routine simpler and more efficient..
        /// </summary>
        public static string AccountCreationMessage {
            get {
                return ResourceManager.GetString("AccountCreationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account deleted successfully..
        /// </summary>
        public static string AccountDeleted {
            get {
                return ResourceManager.GetString("AccountDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        public static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to active-tab.
        /// </summary>
        public static string active_tab {
            get {
                return ResourceManager.GetString("active-tab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string add {
            get {
                return ResourceManager.GetString("add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Alert.
        /// </summary>
        public static string Add_Alert {
            get {
                return ResourceManager.GetString("Add Alert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New DX Alert.
        /// </summary>
        public static string Add_New_DX_Alert {
            get {
                return ResourceManager.GetString("Add New DX Alert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Procedure Alert.
        /// </summary>
        public static string Add_New_Procedure_Alert {
            get {
                return ResourceManager.GetString("Add New Procedure Alert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string Add2 {
            get {
                return ResourceManager.GetString("Add2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddAppointment.
        /// </summary>
        public static string AddAppointment {
            get {
                return ResourceManager.GetString("AddAppointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Info.
        /// </summary>
        public static string AdditionalInfo {
            get {
                return ResourceManager.GetString("AdditionalInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Info must not exceed 300 characters..
        /// </summary>
        public static string AdditionalInfoMaxLength {
            get {
                return ResourceManager.GetString("AdditionalInfoMaxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Info must be at least 5 characters long..
        /// </summary>
        public static string AdditionalInfoMinLength {
            get {
                return ResourceManager.GetString("AdditionalInfoMinLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Info is required!.
        /// </summary>
        public static string AdditionalInfoRequired {
            get {
                return ResourceManager.GetString("AdditionalInfoRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Reviews.
        /// </summary>
        public static string AdditionalsReviews {
            get {
                return ResourceManager.GetString("AdditionalsReviews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Member.
        /// </summary>
        public static string AddMember {
            get {
                return ResourceManager.GetString("AddMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddPageRoleMappingFailed.
        /// </summary>
        public static string AddPageRoleMappingFailed {
            get {
                return ResourceManager.GetString("AddPageRoleMappingFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Product.
        /// </summary>
        public static string AddProduct {
            get {
                return ResourceManager.GetString("AddProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Details.
        /// </summary>
        public static string AddressDetails {
            get {
                return ResourceManager.GetString("AddressDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 1.
        /// </summary>
        public static string AddressLine1 {
            get {
                return ResourceManager.GetString("AddressLine1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Line 2.
        /// </summary>
        public static string AddressLine2 {
            get {
                return ResourceManager.GetString("AddressLine2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddressRetrievalFailure.
        /// </summary>
        public static string AddressRetrievalFailure {
            get {
                return ResourceManager.GetString("AddressRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin.
        /// </summary>
        public static string AdminRole {
            get {
                return ResourceManager.GetString("AdminRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age Lower Bound.
        /// </summary>
        public static string Age_Lower_Bound {
            get {
                return ResourceManager.GetString("Age Lower Bound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Age Upper Bound.
        /// </summary>
        public static string Age_Upper_Bound {
            get {
                return ResourceManager.GetString("Age Upper Bound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent must contain alphabets only..
        /// </summary>
        public static string AgentInvalid {
            get {
                return ResourceManager.GetString("AgentInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent is required..
        /// </summary>
        public static string AgentRequired {
            get {
                return ResourceManager.GetString("AgentRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to With Teya Health, you’ll have access to cutting-edge AI tools that automate mundane tasks, giving you more time to focus on what really matters—caring for your patients and streamlining your workflow..
        /// </summary>
        public static string AIProductsMessage {
            get {
                return ResourceManager.GetString("AIProductsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alert Name.
        /// </summary>
        public static string Alert_Name {
            get {
                return ResourceManager.GetString("Alert Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allergies.
        /// </summary>
        public static string Allergies {
            get {
                return ResourceManager.GetString("Allergies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MedicineId.
        /// </summary>
        public static string AllergiesGridField0 {
            get {
                return ResourceManager.GetString("AllergiesGridField0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Classification.
        /// </summary>
        public static string AllergiesGridField1 {
            get {
                return ResourceManager.GetString("AllergiesGridField1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent.
        /// </summary>
        public static string AllergiesGridField2 {
            get {
                return ResourceManager.GetString("AllergiesGridField2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reaction.
        /// </summary>
        public static string AllergiesGridField3 {
            get {
                return ResourceManager.GetString("AllergiesGridField3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string AllergiesGridField4 {
            get {
                return ResourceManager.GetString("AllergiesGridField4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DrugName.
        /// </summary>
        public static string AllergiesGridField5 {
            get {
                return ResourceManager.GetString("AllergiesGridField5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select drug name.
        /// </summary>
        public static string AllergiesMsg1 {
            get {
                return ResourceManager.GetString("AllergiesMsg1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Classification.
        /// </summary>
        public static string AllergyCol1 {
            get {
                return ResourceManager.GetString("AllergyCol1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agent.
        /// </summary>
        public static string AllergyCol2 {
            get {
                return ResourceManager.GetString("AllergyCol2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reaction.
        /// </summary>
        public static string AllergyCol3 {
            get {
                return ResourceManager.GetString("AllergyCol3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string AllergyCol4 {
            get {
                return ResourceManager.GetString("AllergyCol4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drug Name.
        /// </summary>
        public static string AllergyCol5 {
            get {
                return ResourceManager.GetString("AllergyCol5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        public static string AllergyCol6 {
            get {
                return ResourceManager.GetString("AllergyCol6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TasksRetrievalFailure.
        /// </summary>
        public static string AllergyServiceError1 {
            get {
                return ResourceManager.GetString("AllergyServiceError1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddressRetrievalFailure.
        /// </summary>
        public static string AllergyServiceError2 {
            get {
                return ResourceManager.GetString("AllergyServiceError2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /ambientsolution.
        /// </summary>
        public static string AmbientSolutionRoute {
            get {
                return ResourceManager.GetString("AmbientSolutionRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while fetching visit Status names..
        /// </summary>
        public static string An_error_occurred_while_fetching_visit_Status_names_ {
            get {
                return ResourceManager.GetString("An error occurred while fetching visit Status names.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while fetching visit type names..
        /// </summary>
        public static string An_error_occurred_while_fetching_visit_type_names_ {
            get {
                return ResourceManager.GetString("An error occurred while fetching visit type names.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  API Response: {ResponseData}.
        /// </summary>
        public static string API_Response___ResponseData_ {
            get {
                return ResourceManager.GetString("API Response: {ResponseData}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:DeleteRegistrationMemberUrl.
        /// </summary>
        public static string ApiSettings_DeleteRegistrationMemberUrl {
            get {
                return ResourceManager.GetString("ApiSettings:DeleteRegistrationMemberUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:MembersUrl.
        /// </summary>
        public static string ApiSettings_MembersUrl {
            get {
                return ResourceManager.GetString("ApiSettings:MembersUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:ProductsUrl.
        /// </summary>
        public static string ApiSettings_ProductsUrl {
            get {
                return ResourceManager.GetString("ApiSettings:ProductsUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:RegistrationUrl.
        /// </summary>
        public static string ApiSettings_RegistrationUrl {
            get {
                return ResourceManager.GetString("ApiSettings:RegistrationUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:UpdateAccessUrl.
        /// </summary>
        public static string ApiSettings_UpdateAccessUrl {
            get {
                return ResourceManager.GetString("ApiSettings:UpdateAccessUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to application/json.
        /// </summary>
        public static string application_json {
            get {
                return ResourceManager.GetString("application/json", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Appointment Time.
        /// </summary>
        public static string Appointment_Time {
            get {
                return ResourceManager.GetString("Appointment Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AppointmentRetrievalFailure.
        /// </summary>
        public static string AppointmentRetrievalFailure {
            get {
                return ResourceManager.GetString("AppointmentRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        public static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrival Time.
        /// </summary>
        public static string Arrival_Time {
            get {
                return ResourceManager.GetString("Arrival Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrived.
        /// </summary>
        public static string Arrived {
            get {
                return ResourceManager.GetString("Arrived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessments.
        /// </summary>
        public static string Assessments {
            get {
                return ResourceManager.GetString("Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AudioBaseUrl.
        /// </summary>
        public static string AudioBaseUrl {
            get {
                return ResourceManager.GetString("AudioBaseUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to authentication/logout.
        /// </summary>
        public static string authentication_logout {
            get {
                return ResourceManager.GetString("authentication/logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AuthServiceUpdateFailed.
        /// </summary>
        public static string AuthServiceUpdateFailed {
            get {
                return ResourceManager.GetString("AuthServiceUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Backdrop is Disabled! - When Dialog was Opened.
        /// </summary>
        public static string BackdropDisabledMessage {
            get {
                return ResourceManager.GetString("BackdropDisabledMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to With Teya Health, you’ll have access to cutting-edge AI tools that simplify your daily workflow..
        /// </summary>
        public static string BenefitsMessage {
            get {
                return ResourceManager.GetString("BenefitsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both.
        /// </summary>
        public static string Both {
            get {
                return ResourceManager.GetString("Both", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BP.
        /// </summary>
        public static string BP {
            get {
                return ResourceManager.GetString("BP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand Name.
        /// </summary>
        public static string Brand_Name {
            get {
                return ResourceManager.GetString("Brand Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unleash the full potential of our products now!.
        /// </summary>
        public static string CallToActionMessage {
            get {
                return ResourceManager.GetString("CallToActionMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelled.
        /// </summary>
        public static string Cancelled {
            get {
                return ResourceManager.GetString("Cancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CDC.
        /// </summary>
        public static string CDC {
            get {
                return ResourceManager.GetString("CDC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Changes Successfully.
        /// </summary>
        public static string ChangesCancelled {
            get {
                return ResourceManager.GetString("ChangesCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chest Pain.
        /// </summary>
        public static string ChestPain {
            get {
                return ResourceManager.GetString("ChestPain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choices.
        /// </summary>
        public static string Choices {
            get {
                return ResourceManager.GetString("Choices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Classification must contain alphabets only..
        /// </summary>
        public static string ClassificationInvalid {
            get {
                return ResourceManager.GetString("ClassificationInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Classification is required..
        /// </summary>
        public static string ClassificationRequired {
            get {
                return ResourceManager.GetString("ClassificationRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CMS.gov.
        /// </summary>
        public static string CMS_gov {
            get {
                return ResourceManager.GetString("CMS.gov", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        public static string Comments {
            get {
                return ResourceManager.GetString("Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to companyName.
        /// </summary>
        public static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed.
        /// </summary>
        public static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this patient? This action cannot be undone..
        /// </summary>
        public static string Confirm_Deletion {
            get {
                return ResourceManager.GetString("Confirm.Deletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this license? This action is irreversible..
        /// </summary>
        public static string ConfirmDeleteMessage {
            get {
                return ResourceManager.GetString("ConfirmDeleteMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Delete.
        /// </summary>
        public static string ConfirmDeleteTitle {
            get {
                return ResourceManager.GetString("ConfirmDeleteTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Deletion.
        /// </summary>
        public static string ConfirmDeletion {
            get {
                return ResourceManager.GetString("ConfirmDeletion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        public static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Patient Creation.
        /// </summary>
        public static string ConfirmPatientCreation {
            get {
                return ResourceManager.GetString("ConfirmPatientCreation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save the changes to this license?.
        /// </summary>
        public static string ConfirmSaveMessage {
            get {
                return ResourceManager.GetString("ConfirmSaveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Save.
        /// </summary>
        public static string ConfirmSaveTitle {
            get {
                return ResourceManager.GetString("ConfirmSaveTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Update.
        /// </summary>
        public static string ConfirmUpdate {
            get {
                return ResourceManager.GetString("ConfirmUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Congratulations on creating your account with Teya Health! We’re thrilled to have you on board..
        /// </summary>
        public static string CongratulationsMessage {
            get {
                return ResourceManager.GetString("CongratulationsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        public static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONTACT US.
        /// </summary>
        public static string CONTACTUS {
            get {
                return ResourceManager.GetString("CONTACTUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Co-Pay.
        /// </summary>
        public static string CoPay {
            get {
                return ResourceManager.GetString("CoPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CPTCode.
        /// </summary>
        public static string CPTCode {
            get {
                return ResourceManager.GetString("CPTCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Date.
        /// </summary>
        public static string CreatedDate {
            get {
                return ResourceManager.GetString("CreatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string CreateFirstName {
            get {
                return ResourceManager.GetString("CreateFirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string CreateLastName {
            get {
                return ResourceManager.GetString("CreateLastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CreateUser.
        /// </summary>
        public static string CreateUser {
            get {
                return ResourceManager.GetString("CreateUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Medication.
        /// </summary>
        public static string Current_Medication {
            get {
                return ResourceManager.GetString("Current Medication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CustomMessage.
        /// </summary>
        public static string CustomMessage {
            get {
                return ResourceManager.GetString("CustomMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A database error occurred..
        /// </summary>
        public static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date:.
        /// </summary>
        public static string Date_ {
            get {
                return ResourceManager.GetString("Date:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Added.
        /// </summary>
        public static string Date_Added {
            get {
                return ResourceManager.GetString("Date Added", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth.
        /// </summary>
        public static string Date_of_Birth {
            get {
                return ResourceManager.GetString("Date of Birth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Deceased.
        /// </summary>
        public static string DateDeceased {
            get {
                return ResourceManager.GetString("DateDeceased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateOfBirth.
        /// </summary>
        public static string DateOfBirth {
            get {
                return ResourceManager.GetString("DateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DEAInvalidFormat&quot;, &quot;Format: 2 letters followed by 7 digits (e.g., AB1234567)..
        /// </summary>
        public static string DEAInvalidFormat {
            get {
                return ResourceManager.GetString("DEAInvalidFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DEA number.
        /// </summary>
        public static string DEANumber {
            get {
                return ResourceManager.GetString("DEANumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deceased Date.
        /// </summary>
        public static string DeceasedDate {
            get {
                return ResourceManager.GetString("DeceasedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deceased Reason.
        /// </summary>
        public static string DeceasedReason {
            get {
                return ResourceManager.GetString("DeceasedReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Billing Facility.
        /// </summary>
        public static string DefaultBillingFacility {
            get {
                return ResourceManager.GetString("DefaultBillingFacility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 500.
        /// </summary>
        public static string DelayMilliseconds {
            get {
                return ResourceManager.GetString("DelayMilliseconds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Account.
        /// </summary>
        public static string DeleteAccount {
            get {
                return ResourceManager.GetString("DeleteAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to delete this entry?.
        /// </summary>
        public static string DeleteConfirmationMessage {
            get {
                return ResourceManager.GetString("DeleteConfirmationMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deleting account..
        /// </summary>
        public static string DeleteError {
            get {
                return ResourceManager.GetString("DeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeleteFailedStatusCode.
        /// </summary>
        public static string DeleteFailedStatusCode {
            get {
                return ResourceManager.GetString("DeleteFailedStatusCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item is not deleted.
        /// </summary>
        public static string DeleteLogError {
            get {
                return ResourceManager.GetString("DeleteLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Successful.
        /// </summary>
        public static string DeleteSuccessful {
            get {
                return ResourceManager.GetString("DeleteSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denied.
        /// </summary>
        public static string Denied {
            get {
                return ResourceManager.GetString("Denied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Denied/New Rx to follow.
        /// </summary>
        public static string Denied_New_Rx_to_follow {
            get {
                return ResourceManager.GetString("Denied/New Rx to follow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to department.
        /// </summary>
        public static string Department {
            get {
                return ResourceManager.GetString("Department", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DI.
        /// </summary>
        public static string DI {
            get {
                return ResourceManager.GetString("DI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DI Alerts.
        /// </summary>
        public static string DI_Alerts {
            get {
                return ResourceManager.GetString("DI Alerts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabilities.
        /// </summary>
        public static string Disabilities {
            get {
                return ResourceManager.GetString("Disabilities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to displayName.
        /// </summary>
        public static string Display_Name {
            get {
                return ResourceManager.GetString("Display_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string DisplayName {
            get {
                return ResourceManager.GetString("DisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Of Birth.
        /// </summary>
        public static string DOB {
            get {
                return ResourceManager.GetString("DOB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doctor Name.
        /// </summary>
        public static string Doctor_Name {
            get {
                return ResourceManager.GetString("Doctor Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dosage &amp; InTake.
        /// </summary>
        public static string Dosage___InTake {
            get {
                return ResourceManager.GetString("Dosage & InTake", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drug Details.
        /// </summary>
        public static string Drug_Details {
            get {
                return ResourceManager.GetString("Drug Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration.
        /// </summary>
        public static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DX Alerts.
        /// </summary>
        public static string DX_Alerts {
            get {
                return ResourceManager.GetString("DX Alerts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to E-Prescription Type.
        /// </summary>
        public static string E_Prescription_Type {
            get {
                return ResourceManager.GetString("E-Prescription Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Medication.
        /// </summary>
        public static string Edit_Medication {
            get {
                return ResourceManager.GetString("Edit Medication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EditUser.
        /// </summary>
        public static string EditUser {
            get {
                return ResourceManager.GetString("EditUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Effective Date.
        /// </summary>
        public static string EffectiveDate {
            get {
                return ResourceManager.GetString("EffectiveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unleash the full potential of our products now! Your daily tasks are about to become easier, faster, and more efficient..
        /// </summary>
        public static string EfficiencyMessage {
            get {
                return ResourceManager.GetString("EfficiencyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EHR INTEGRATIONS.
        /// </summary>
        public static string EHRINTEGRATIONS {
            get {
                return ResourceManager.GetString("EHRINTEGRATIONS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Already Exists.
        /// </summary>
        public static string Email_Already_Exists {
            get {
                return ResourceManager.GetString("Email Already Exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email is Required.
        /// </summary>
        public static string Email_is_Required {
            get {
                return ResourceManager.GetString("Email is Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to emailAddress.
        /// </summary>
        public static string emailAddress {
            get {
                return ResourceManager.GetString("emailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello, your account has been activated!.
        /// </summary>
        public static string EmailBody {
            get {
                return ResourceManager.GetString("EmailBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string EmailLabel {
            get {
                return ResourceManager.GetString("EmailLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EmailRequired.
        /// </summary>
        public static string EmailRequired {
            get {
                return ResourceManager.GetString("EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string <NAME_EMAIL>.
        /// </summary>
        public static string EmailSenderAddress {
            get {
                return ResourceManager.GetString("EmailSenderAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Health.
        /// </summary>
        public static string EmailSubject {
            get {
                return ResourceManager.GetString("EmailSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer.
        /// </summary>
        public static string Employer {
            get {
                return ResourceManager.GetString("Employer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Address.
        /// </summary>
        public static string EmployerAddress {
            get {
                return ResourceManager.GetString("EmployerAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Address Line 2.
        /// </summary>
        public static string EmployerAddressLine2 {
            get {
                return ResourceManager.GetString("EmployerAddressLine2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer City.
        /// </summary>
        public static string EmployerCity {
            get {
                return ResourceManager.GetString("EmployerCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Country.
        /// </summary>
        public static string EmployerCountry {
            get {
                return ResourceManager.GetString("EmployerCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Details.
        /// </summary>
        public static string EmployerDetails {
            get {
                return ResourceManager.GetString("EmployerDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Name.
        /// </summary>
        public static string EmployerName {
            get {
                return ResourceManager.GetString("EmployerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer State.
        /// </summary>
        public static string EmployerState {
            get {
                return ResourceManager.GetString("EmployerState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Encounter Notes.
        /// </summary>
        public static string Encounter_Notes {
            get {
                return ResourceManager.GetString("Encounter Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date.
        /// </summary>
        public static string End_Date {
            get {
                return ResourceManager.GetString("End Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateTime.MinValue.
        /// </summary>
        public static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EndTime:.
        /// </summary>
        public static string EndTime_ {
            get {
                return ResourceManager.GetString("EndTime:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Notes.
        /// </summary>
        public static string Enter_Notes {
            get {
                return ResourceManager.GetString("Enter Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Reason.
        /// </summary>
        public static string Enter_Reason {
            get {
                return ResourceManager.GetString("Enter Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Room Number.
        /// </summary>
        public static string Enter_Room_Number {
            get {
                return ResourceManager.GetString("Enter Room Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Search Term.
        /// </summary>
        public static string Enter_Search_Term {
            get {
                return ResourceManager.GetString("Enter Search Term", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterCustomMessage.
        /// </summary>
        public static string EnterCustomMessage {
            get {
                return ResourceManager.GetString("EnterCustomMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterDisplayName.
        /// </summary>
        public static string EnterDisplayName {
            get {
                return ResourceManager.GetString("EnterDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterEmailAddress.
        /// </summary>
        public static string EnterEmailAddress {
            get {
                return ResourceManager.GetString("EnterEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterNotes.
        /// </summary>
        public static string EnterNotes {
            get {
                return ResourceManager.GetString("EnterNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterReason.
        /// </summary>
        public static string EnterReason {
            get {
                return ResourceManager.GetString("EnterReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterRedirectUrl.
        /// </summary>
        public static string EnterRedirectUrl {
            get {
                return ResourceManager.GetString("EnterRedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterSearchTerm.
        /// </summary>
        public static string EnterSearchTerm {
            get {
                return ResourceManager.GetString("EnterSearchTerm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while processing..
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deserializing response: {0}.
        /// </summary>
        public static string Error_deserializing_response___0_ {
            get {
                return ResourceManager.GetString("Error deserializing response: {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error fetching full user details.
        /// </summary>
        public static string Error_fetching_full_user_details {
            get {
                return ResourceManager.GetString("Error fetching full user details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error loading organizations:.
        /// </summary>
        public static string Error_loading_organizations_ {
            get {
                return ResourceManager.GetString("Error loading organizations:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error parsing user details.
        /// </summary>
        public static string Error_parsing_user_details {
            get {
                return ResourceManager.GetString("Error parsing user details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error registering member.
        /// </summary>
        public static string Error_registering_member {
            get {
                return ResourceManager.GetString("Error registering member", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error retrieving TherapeuticInterventionsList codes:.
        /// </summary>
        public static string Error_retrieving_TherapeuticInterventionsList_codes_ {
            get {
                return ResourceManager.GetString("Error retrieving TherapeuticInterventionsList codes:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error adding free user license.
        /// </summary>
        public static string ErrorAddingLicense {
            get {
                return ResourceManager.GetString("ErrorAddingLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingPageRoleMapping.
        /// </summary>
        public static string ErrorAddingPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorAddingPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingVisitType.
        /// </summary>
        public static string ErrorAddingVisitType {
            get {
                return ResourceManager.GetString("ErrorAddingVisitType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorCreatingUser.
        /// </summary>
        public static string ErrorCreatingUser {
            get {
                return ResourceManager.GetString("ErrorCreatingUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deleting patient. Please try again..
        /// </summary>
        public static string Errordelete {
            get {
                return ResourceManager.GetString("Errordelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingFacility.
        /// </summary>
        public static string ErrorDeletingFacility {
            get {
                return ResourceManager.GetString("ErrorDeletingFacility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingMember.
        /// </summary>
        public static string ErrorDeletingMember {
            get {
                return ResourceManager.GetString("ErrorDeletingMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingOrganization.
        /// </summary>
        public static string ErrorDeletingOrganization {
            get {
                return ResourceManager.GetString("ErrorDeletingOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingPageRoleMapping.
        /// </summary>
        public static string ErrorDeletingPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorDeletingPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingRole.
        /// </summary>
        public static string ErrorDeletingRole {
            get {
                return ResourceManager.GetString("ErrorDeletingRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingUsers.
        /// </summary>
        public static string ErrorDeletingUsers {
            get {
                return ResourceManager.GetString("ErrorDeletingUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingVisitType.
        /// </summary>
        public static string ErrorDeletingVisitType {
            get {
                return ResourceManager.GetString("ErrorDeletingVisitType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDuringMemberSubmissionUpdate.
        /// </summary>
        public static string ErrorDuringMemberSubmissionUpdate {
            get {
                return ResourceManager.GetString("ErrorDuringMemberSubmissionUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error During Registration.
        /// </summary>
        public static string ErrorDuringRegistration {
            get {
                return ResourceManager.GetString("ErrorDuringRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllFacilities.
        /// </summary>
        public static string ErrorFetchingAllFacilities {
            get {
                return ResourceManager.GetString("ErrorFetchingAllFacilities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllFacilitylists.
        /// </summary>
        public static string ErrorFetchingAllFacilitylists {
            get {
                return ResourceManager.GetString("ErrorFetchingAllFacilitylists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllOrganizations.
        /// </summary>
        public static string ErrorFetchingAllOrganizations {
            get {
                return ResourceManager.GetString("ErrorFetchingAllOrganizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllPageRoleMappings.
        /// </summary>
        public static string ErrorFetchingAllPageRoleMappings {
            get {
                return ResourceManager.GetString("ErrorFetchingAllPageRoleMappings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllPredefinedVisitTypes.
        /// </summary>
        public static string ErrorFetchingAllPredefinedVisitTypes {
            get {
                return ResourceManager.GetString("ErrorFetchingAllPredefinedVisitTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllRoles.
        /// </summary>
        public static string ErrorFetchingAllRoles {
            get {
                return ResourceManager.GetString("ErrorFetchingAllRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllRoleslists.
        /// </summary>
        public static string ErrorFetchingAllRoleslists {
            get {
                return ResourceManager.GetString("ErrorFetchingAllRoleslists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingDiagnosis.
        /// </summary>
        public static string ErrorFetchingDiagnosis {
            get {
                return ResourceManager.GetString("ErrorFetchingDiagnosis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingFacilities.
        /// </summary>
        public static string ErrorFetchingFacilities {
            get {
                return ResourceManager.GetString("ErrorFetchingFacilities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingFacilityById.
        /// </summary>
        public static string ErrorFetchingFacilityById {
            get {
                return ResourceManager.GetString("ErrorFetchingFacilityById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingFacilityNames.
        /// </summary>
        public static string ErrorFetchingFacilityNames {
            get {
                return ResourceManager.GetString("ErrorFetchingFacilityNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingFullUserDetails.
        /// </summary>
        public static string ErrorFetchingFullUserDetails {
            get {
                return ResourceManager.GetString("ErrorFetchingFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Licenses.
        /// </summary>
        public static string ErrorFetchingLicenses {
            get {
                return ResourceManager.GetString("ErrorFetchingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingMemberById.
        /// </summary>
        public static string ErrorFetchingMemberById {
            get {
                return ResourceManager.GetString("ErrorFetchingMemberById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Members For Product.
        /// </summary>
        public static string ErrorFetchingMembersForProduct {
            get {
                return ResourceManager.GetString("ErrorFetchingMembersForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingOrganizationById.
        /// </summary>
        public static string ErrorFetchingOrganizationById {
            get {
                return ResourceManager.GetString("ErrorFetchingOrganizationById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingOrganizations.
        /// </summary>
        public static string ErrorFetchingOrganizations {
            get {
                return ResourceManager.GetString("ErrorFetchingOrganizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingOrganizationsForProduct.
        /// </summary>
        public static string ErrorFetchingOrganizationsForProduct {
            get {
                return ResourceManager.GetString("ErrorFetchingOrganizationsForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPageRoleMappingById.
        /// </summary>
        public static string ErrorFetchingPageRoleMappingById {
            get {
                return ResourceManager.GetString("ErrorFetchingPageRoleMappingById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPagesByRoleId.
        /// </summary>
        public static string ErrorFetchingPagesByRoleId {
            get {
                return ResourceManager.GetString("ErrorFetchingPagesByRoleId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error fetching products.
        /// </summary>
        public static string ErrorFetchingProducts {
            get {
                return ResourceManager.GetString("ErrorFetchingProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingProviderTypes.
        /// </summary>
        public static string ErrorFetchingProviderTypes {
            get {
                return ResourceManager.GetString("ErrorFetchingProviderTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingRoleById.
        /// </summary>
        public static string ErrorFetchingRoleById {
            get {
                return ResourceManager.GetString("ErrorFetchingRoleById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingRoleNames.
        /// </summary>
        public static string ErrorFetchingRoleNames {
            get {
                return ResourceManager.GetString("ErrorFetchingRoleNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingRoles.
        /// </summary>
        public static string ErrorFetchingRoles {
            get {
                return ResourceManager.GetString("ErrorFetchingRoles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingUsers.
        /// </summary>
        public static string ErrorFetchingUsers {
            get {
                return ResourceManager.GetString("ErrorFetchingUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingVisitStatus.
        /// </summary>
        public static string ErrorFetchingVisitStatus {
            get {
                return ResourceManager.GetString("ErrorFetchingVisitStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingVisitTypes.
        /// </summary>
        public static string ErrorFetchingVisitTypes {
            get {
                return ResourceManager.GetString("ErrorFetchingVisitTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorGettingFullUserDetails.
        /// </summary>
        public static string ErrorGettingFullUserDetails {
            get {
                return ResourceManager.GetString("ErrorGettingFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorGettingUserDetails.
        /// </summary>
        public static string ErrorGettingUserDetails {
            get {
                return ResourceManager.GetString("ErrorGettingUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorInvitingUser.
        /// </summary>
        public static string ErrorInvitingUser {
            get {
                return ResourceManager.GetString("ErrorInvitingUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorParsingUserDetails.
        /// </summary>
        public static string ErrorParsingUserDetails {
            get {
                return ResourceManager.GetString("ErrorParsingUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorRegisteringMember.
        /// </summary>
        public static string ErrorRegisteringMember {
            get {
                return ResourceManager.GetString("ErrorRegisteringMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Licenses.
        /// </summary>
        public static string ErrorSavingLicenses {
            get {
                return ResourceManager.GetString("ErrorSavingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Member Access Updates.
        /// </summary>
        public static string ErrorSavingMemberAccessUpdates {
            get {
                return ResourceManager.GetString("ErrorSavingMemberAccessUpdates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingFacilityData.
        /// </summary>
        public static string ErrorSubmittingFacilityData {
            get {
                return ResourceManager.GetString("ErrorSubmittingFacilityData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingMemberData.
        /// </summary>
        public static string ErrorSubmittingMemberData {
            get {
                return ResourceManager.GetString("ErrorSubmittingMemberData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingOrganizationData.
        /// </summary>
        public static string ErrorSubmittingOrganizationData {
            get {
                return ResourceManager.GetString("ErrorSubmittingOrganizationData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingRoleData.
        /// </summary>
        public static string ErrorSubmittingRoleData {
            get {
                return ResourceManager.GetString("ErrorSubmittingRoleData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingFacility.
        /// </summary>
        public static string ErrorUpdatingFacility {
            get {
                return ResourceManager.GetString("ErrorUpdatingFacility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingOrganization.
        /// </summary>
        public static string ErrorUpdatingOrganization {
            get {
                return ResourceManager.GetString("ErrorUpdatingOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingPageRoleMapping.
        /// </summary>
        public static string ErrorUpdatingPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorUpdatingPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingRole.
        /// </summary>
        public static string ErrorUpdatingRole {
            get {
                return ResourceManager.GetString("ErrorUpdatingRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ethnicity.
        /// </summary>
        public static string Ethnicity {
            get {
                return ResourceManager.GetString("Ethnicity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExceptionOccurred.
        /// </summary>
        public static string ExceptionOccurred {
            get {
                return ResourceManager.GetString("ExceptionOccurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        public static string Exit {
            get {
                return ResourceManager.GetString("Exit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExtensionCompanyName.
        /// </summary>
        public static string ExtensionCompanyName {
            get {
                return ResourceManager.GetString("ExtensionCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to External ID.
        /// </summary>
        public static string ExternalID {
            get {
                return ResourceManager.GetString("ExternalID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Facilities.
        /// </summary>
        public static string Facilities {
            get {
                return ResourceManager.GetString("Facilities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityAlreadyExists.
        /// </summary>
        public static string FacilityAlreadyExists {
            get {
                return ResourceManager.GetString("FacilityAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityDeletedSuccessfully.
        /// </summary>
        public static string FacilityDeletedSuccessfully {
            get {
                return ResourceManager.GetString("FacilityDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityDeletionFailed.
        /// </summary>
        public static string FacilityDeletionFailed {
            get {
                return ResourceManager.GetString("FacilityDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityRegistered.
        /// </summary>
        public static string FacilityRegistered {
            get {
                return ResourceManager.GetString("FacilityRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityRegistrationError.
        /// </summary>
        public static string FacilityRegistrationError {
            get {
                return ResourceManager.GetString("FacilityRegistrationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save user data..
        /// </summary>
        public static string Failed {
            get {
                return ResourceManager.GetString("Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed Adding User.
        /// </summary>
        public static string FailedAddingUser {
            get {
                return ResourceManager.GetString("FailedAddingUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to upload Image.
        /// </summary>
        public static string FailedImage {
            get {
                return ResourceManager.GetString("FailedImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to remove Image..
        /// </summary>
        public static string FailedImages {
            get {
                return ResourceManager.GetString("FailedImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToGetFullUserDetails.
        /// </summary>
        public static string FailedToGetFullUserDetails {
            get {
                return ResourceManager.GetString("FailedToGetFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToGetUserDetails.
        /// </summary>
        public static string FailedToGetUserDetails {
            get {
                return ResourceManager.GetString("FailedToGetUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToSendInvitation.
        /// </summary>
        public static string FailedToSendInvitation {
            get {
                return ResourceManager.GetString("FailedToSendInvitation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToUpdateUser.
        /// </summary>
        public static string FailedToUpdateUser {
            get {
                return ResourceManager.GetString("FailedToUpdateUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Family Size.
        /// </summary>
        public static string FamilySize {
            get {
                return ResourceManager.GetString("FamilySize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FDB.
        /// </summary>
        public static string FDB {
            get {
                return ResourceManager.GetString("FDB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Federal TaxId.
        /// </summary>
        public static string FederalTaxId {
            get {
                return ResourceManager.GetString("FederalTaxId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Female.
        /// </summary>
        public static string Female {
            get {
                return ResourceManager.GetString("Female", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fetched 1 license for organization: {0}.
        /// </summary>
        public static string FetchedOneLicense {
            get {
                return ResourceManager.GetString("FetchedOneLicense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fetching licenses for organization: {0}.
        /// </summary>
        public static string FetchingLicenses {
            get {
                return ResourceManager.GetString("FetchingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File too large. Maximum size is 10MB..
        /// </summary>
        public static string Filetoolarge {
            get {
                return ResourceManager.GetString("Filetoolarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financial Review Date.
        /// </summary>
        public static string FinancialReviewDate {
            get {
                return ResourceManager.GetString("FinancialReviewDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name is required!.
        /// </summary>
        public static string First_Name_is_required_ {
            get {
                return ResourceManager.GetString("First Name is required!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0.
        /// </summary>
        public static string FirstIndex {
            get {
                return ResourceManager.GetString("FirstIndex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name is required!.
        /// </summary>
        public static string FirstNameRequired {
            get {
                return ResourceManager.GetString("FirstNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free user license added for organization {0}.
        /// </summary>
        public static string FreeLicenseAdded {
            get {
                return ResourceManager.GetString("FreeLicenseAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Functionals.
        /// </summary>
        public static string Functionals {
            get {
                return ResourceManager.GetString("Functionals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string <NAME_EMAIL>.
        /// </summary>
        public static string Gaurang_mail {
            get {
                return ResourceManager.GetString("Gaurang_mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gender.
        /// </summary>
        public static string Gender {
            get {
                return ResourceManager.GetString("Gender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GetByIdFailed.
        /// </summary>
        public static string GetByIdFailed {
            get {
                return ResourceManager.GetString("GetByIdFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while fetching members..
        /// </summary>
        public static string GetLogError {
            get {
                return ResourceManager.GetString("GetLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GetPagesByRoleIdFailed.
        /// </summary>
        public static string GetPagesByRoleIdFailed {
            get {
                return ResourceManager.GetString("GetPagesByRoleIdFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GivenName.
        /// </summary>
        public static string Given_Name {
            get {
                return ResourceManager.GetString("Given Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string GivenName {
            get {
                return ResourceManager.GetString("GivenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear.
        /// </summary>
        public static string Greeting {
            get {
                return ResourceManager.GetString("Greeting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear.
        /// </summary>
        public static string GreetingMessage {
            get {
                return ResourceManager.GetString("GreetingMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Number.
        /// </summary>
        public static string GroupNumber {
            get {
                return ResourceManager.GetString("GroupNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Growth Chart.
        /// </summary>
        public static string Growth_Chart {
            get {
                return ResourceManager.GetString("Growth Chart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian.
        /// </summary>
        public static string Guardian {
            get {
                return ResourceManager.GetString("Guardian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Address.
        /// </summary>
        public static string GuardianAddress {
            get {
                return ResourceManager.GetString("GuardianAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian City.
        /// </summary>
        public static string GuardianCity {
            get {
                return ResourceManager.GetString("GuardianCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Country.
        /// </summary>
        public static string GuardianCountry {
            get {
                return ResourceManager.GetString("GuardianCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Details.
        /// </summary>
        public static string GuardianDetails {
            get {
                return ResourceManager.GetString("GuardianDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Email.
        /// </summary>
        public static string GuardianEmail {
            get {
                return ResourceManager.GetString("GuardianEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Name.
        /// </summary>
        public static string GuardianName {
            get {
                return ResourceManager.GetString("GuardianName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Phone.
        /// </summary>
        public static string GuardianPhone {
            get {
                return ResourceManager.GetString("GuardianPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Relationship.
        /// </summary>
        public static string GuardianRelationship {
            get {
                return ResourceManager.GetString("GuardianRelationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian Sex.
        /// </summary>
        public static string GuardianSex {
            get {
                return ResourceManager.GetString("GuardianSex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian SSIN.
        /// </summary>
        public static string GuardianSSIN {
            get {
                return ResourceManager.GetString("GuardianSSIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian State.
        /// </summary>
        public static string GuardianState {
            get {
                return ResourceManager.GetString("GuardianState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guest.
        /// </summary>
        public static string Guest {
            get {
                return ResourceManager.GetString("Guest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to U.
        /// </summary>
        public static string GusestUser {
            get {
                return ResourceManager.GetString("GusestUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hospitalization.
        /// </summary>
        public static string Hospitalization {
            get {
                return ResourceManager.GetString("Hospitalization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTTP Request Error:.
        /// </summary>
        public static string HTTPRequestError {
            get {
                return ResourceManager.GetString("HTTPRequestError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to id.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image file size must be under 10MB..
        /// </summary>
        public static string Imagefilesize {
            get {
                return ResourceManager.GetString("Imagefilesize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Immunization.
        /// </summary>
        public static string Immunization {
            get {
                return ResourceManager.GetString("Immunization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Progress.
        /// </summary>
        public static string In_Progress {
            get {
                return ResourceManager.GetString("In Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive Organization:.
        /// </summary>
        public static string Inactive_Organization_ {
            get {
                return ResourceManager.GetString("Inactive Organization:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry.
        /// </summary>
        public static string Industry {
            get {
                return ResourceManager.GetString("Industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance.
        /// </summary>
        public static string Insurance {
            get {
                return ResourceManager.GetString("Insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Details.
        /// </summary>
        public static string InsuranceDetails {
            get {
                return ResourceManager.GetString("InsuranceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Appointment Date or Time.
        /// </summary>
        public static string Invalid_Appointment_Date_or_Time {
            get {
                return ResourceManager.GetString("Invalid Appointment Date or Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Email Message.
        /// </summary>
        public static string Invalid_Email_Message {
            get {
                return ResourceManager.GetString("Invalid Email Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidEmailFormat.
        /// </summary>
        public static string InvalidEmailFormat {
            get {
                return ResourceManager.GetString("InvalidEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email input correct format.
        /// </summary>
        public static string InvalidEmailMessage {
            get {
                return ResourceManager.GetString("InvalidEmailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidFacility.
        /// </summary>
        public static string InvalidFacility {
            get {
                return ResourceManager.GetString("InvalidFacility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file type. Please upload an image file..
        /// </summary>
        public static string Invalidfiletype {
            get {
                return ResourceManager.GetString("Invalidfiletype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Id is not valid.
        /// </summary>
        public static string InvalidId {
            get {
                return ResourceManager.GetString("InvalidId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not a valid member.
        /// </summary>
        public static string InvalidMember {
            get {
                return ResourceManager.GetString("InvalidMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidOrganization.
        /// </summary>
        public static string InvalidOrganization {
            get {
                return ResourceManager.GetString("InvalidOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidPatientIds.
        /// </summary>
        public static string InvalidPatientIds {
            get {
                return ResourceManager.GetString("InvalidPatientIds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidRole.
        /// </summary>
        public static string InvalidRole {
            get {
                return ResourceManager.GetString("InvalidRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteRedirectUrl {
            get {
                return ResourceManager.GetString("InviteRedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteRedirectUrlDev {
            get {
                return ResourceManager.GetString("InviteRedirectUrlDev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteUrl {
            get {
                return ResourceManager.GetString("InviteUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InviteUser.
        /// </summary>
        public static string InviteUser {
            get {
                return ResourceManager.GetString("InviteUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /inviteuser.
        /// </summary>
        public static string InviteUserPath {
            get {
                return ResourceManager.GetString("InviteUserPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Itchy Eyes.
        /// </summary>
        public static string ItchyEyes {
            get {
                return ResourceManager.GetString("ItchyEyes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Description must not exceed 200 characters..
        /// </summary>
        public static string JobDescriptionMaxLength {
            get {
                return ResourceManager.GetString("JobDescriptionMaxLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Description must be at least 10 characters long..
        /// </summary>
        public static string JobDescriptionMinLength {
            get {
                return ResourceManager.GetString("JobDescriptionMinLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Title.
        /// </summary>
        public static string JobTitle {
            get {
                return ResourceManager.GetString("JobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while deserializing the response: {0}.
        /// </summary>
        public static string JsonDeserializationError {
            get {
                return ResourceManager.GetString("JsonDeserializationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Labs.
        /// </summary>
        public static string Labs {
            get {
                return ResourceManager.GetString("Labs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name is required!.
        /// </summary>
        public static string Last_Name_is_required_ {
            get {
                return ResourceManager.GetString("Last Name is required!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License Retrieval Failure.
        /// </summary>
        public static string LicenseRetrievalFailure {
            get {
                return ResourceManager.GetString("LicenseRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limitations.
        /// </summary>
        public static string Limitations {
            get {
                return ResourceManager.GetString("Limitations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Pharmacy.
        /// </summary>
        public static string Link_Pharmacy {
            get {
                return ResourceManager.GetString("Link Pharmacy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading Products.
        /// </summary>
        public static string LoadingProducts {
            get {
                return ResourceManager.GetString("LoadingProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LoadingUserData.
        /// </summary>
        public static string LoadingUserData {
            get {
                return ResourceManager.GetString("LoadingUserData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOGIN.
        /// </summary>
        public static string LOGIN {
            get {
                return ResourceManager.GetString("LOGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Here.
        /// </summary>
        public static string LoginButtonText {
            get {
                return ResourceManager.GetString("LoginButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        public static string LoginButtonText1 {
            get {
                return ResourceManager.GetString("LoginButtonText1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log in to your account.
        /// </summary>
        public static string LoginCTA {
            get {
                return ResourceManager.GetString("LoginCTA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your login details are as follows:.
        /// </summary>
        public static string LoginDetailsMessage {
            get {
                return ResourceManager.GetString("LoginDetailsMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Long Term Goals.
        /// </summary>
        public static string Long_Term_Goals {
            get {
                return ResourceManager.GetString("Long Term Goals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mail.
        /// </summary>
        public static string Mail {
            get {
                return ResourceManager.GetString("Mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MailNickname.
        /// </summary>
        public static string MailNickname {
            get {
                return ResourceManager.GetString("MailNickname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string <NAME_EMAIL>.
        /// </summary>
        public static string Mak_mail {
            get {
                return ResourceManager.GetString("Mak_mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Male.
        /// </summary>
        public static string Male {
            get {
                return ResourceManager.GetString("Male", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to city.
        /// </summary>
        public static string ManageCity {
            get {
                return ResourceManager.GetString("ManageCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to companyName.
        /// </summary>
        public static string ManageCompanyName {
            get {
                return ResourceManager.GetString("ManageCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to country.
        /// </summary>
        public static string ManageCountry {
            get {
                return ResourceManager.GetString("ManageCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to department.
        /// </summary>
        public static string ManageDepartment {
            get {
                return ResourceManager.GetString("ManageDepartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to displayName.
        /// </summary>
        public static string ManageDisplayName {
            get {
                return ResourceManager.GetString("ManageDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to extensionCompanyName.
        /// </summary>
        public static string ManageExtensionCompanyName {
            get {
                return ResourceManager.GetString("ManageExtensionCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to givenName.
        /// </summary>
        public static string ManageGivenName {
            get {
                return ResourceManager.GetString("ManageGivenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to id.
        /// </summary>
        public static string ManageId {
            get {
                return ResourceManager.GetString("ManageId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to jobTitle.
        /// </summary>
        public static string ManageJobTitle {
            get {
                return ResourceManager.GetString("ManageJobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mail.
        /// </summary>
        public static string ManageMail {
            get {
                return ResourceManager.GetString("ManageMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mobilePhone.
        /// </summary>
        public static string ManageMobilePhone {
            get {
                return ResourceManager.GetString("ManageMobilePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to officeLocation.
        /// </summary>
        public static string ManageOfficeLocation {
            get {
                return ResourceManager.GetString("ManageOfficeLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to postalCode.
        /// </summary>
        public static string ManagePostalCode {
            get {
                return ResourceManager.GetString("ManagePostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Products For.
        /// </summary>
        public static string ManageProductsFor {
            get {
                return ResourceManager.GetString("ManageProductsFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /manageprofile.
        /// </summary>
        public static string ManageProfile {
            get {
                return ResourceManager.GetString("ManageProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to state.
        /// </summary>
        public static string ManageState {
            get {
                return ResourceManager.GetString("ManageState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to streetAddress.
        /// </summary>
        public static string ManageStreetAddress {
            get {
                return ResourceManager.GetString("ManageStreetAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to surname.
        /// </summary>
        public static string ManageSurname {
            get {
                return ResourceManager.GetString("ManageSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to userType.
        /// </summary>
        public static string ManageUserType {
            get {
                return ResourceManager.GetString("ManageUserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Your Teya Account.
        /// </summary>
        public static string ManagYourTeyaAccount {
            get {
                return ResourceManager.GetString("ManagYourTeyaAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marital Status.
        /// </summary>
        public static string MaritalStatus {
            get {
                return ResourceManager.GetString("MaritalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medical Summary.
        /// </summary>
        public static string Medical_Summary {
            get {
                return ResourceManager.GetString("Medical Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medications.
        /// </summary>
        public static string Medications {
            get {
                return ResourceManager.GetString("Medications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member registered successfully!.
        /// </summary>
        public static string Member_registered_successfully_ {
            get {
                return ResourceManager.GetString("Member registered successfully!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberDeleted.
        /// </summary>
        public static string MemberDeleted {
            get {
                return ResourceManager.GetString("MemberDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberDeletedSuccessfully.
        /// </summary>
        public static string MemberDeletedSuccessfully {
            get {
                return ResourceManager.GetString("MemberDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberDeletionFailed.
        /// </summary>
        public static string MemberDeletionFailed {
            get {
                return ResourceManager.GetString("MemberDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member Details.
        /// </summary>
        public static string MemberDetails {
            get {
                return ResourceManager.GetString("MemberDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member Not Found.
        /// </summary>
        public static string MemberNotFound {
            get {
                return ResourceManager.GetString("MemberNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberRegistered.
        /// </summary>
        public static string MemberRegistered {
            get {
                return ResourceManager.GetString("MemberRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberRegisteredSuccessfully.
        /// </summary>
        public static string MemberRegisteredSuccessfully {
            get {
                return ResourceManager.GetString("MemberRegisteredSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred during member registration..
        /// </summary>
        public static string MemberRegistrationError {
            get {
                return ResourceManager.GetString("MemberRegistrationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to http://localhost/MemberServiceApi/api/Products.
        /// </summary>
        public static string MemberServiceApi_base_url {
            get {
                return ResourceManager.GetString("MemberServiceApi_base_url", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberUpdated.
        /// </summary>
        public static string MemberUpdated {
            get {
                return ResourceManager.GetString("MemberUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /Menu.
        /// </summary>
        public static string MenuPageRoute {
            get {
                return ResourceManager.GetString("MenuPageRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mic.
        /// </summary>
        public static string Mic {
            get {
                return ResourceManager.GetString("Mic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Name.
        /// </summary>
        public static string MiddleName {
            get {
                return ResourceManager.GetString("MiddleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle Name should be at least 2 characters long..
        /// </summary>
        public static string MiddleNameMinLength {
            get {
                return ResourceManager.GetString("MiddleNameMinLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minutes.
        /// </summary>
        public static string Minutes {
            get {
                return ResourceManager.GetString("Minutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Misc.
        /// </summary>
        public static string Misc {
            get {
                return ResourceManager.GetString("Misc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Miscellaneous Details.
        /// </summary>
        public static string MiscellaneousDetails {
            get {
                return ResourceManager.GetString("MiscellaneousDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MobilePhone.
        /// </summary>
        public static string MobilePhone {
            get {
                return ResourceManager.GetString("MobilePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly Income.
        /// </summary>
        public static string MonthlyIncome {
            get {
                return ResourceManager.GetString("MonthlyIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to name.
        /// </summary>
        public static string NameClaim {
            get {
                return ResourceManager.GetString("NameClaim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nationality.
        /// </summary>
        public static string Nationality {
            get {
                return ResourceManager.GetString("Nationality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /externalusercreation.
        /// </summary>
        public static string NavigateToExternalUserPage {
            get {
                return ResourceManager.GetString("NavigateToExternalUserPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /sendmail.
        /// </summary>
        public static string NavigateToSendMailPage {
            get {
                return ResourceManager.GetString("NavigateToSendMailPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /usermanagement.
        /// </summary>
        public static string NavigateToUserManagementPage {
            get {
                return ResourceManager.GetString("NavigateToUserManagementPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /usercreation.
        /// </summary>
        public static string NavigateToUserPage {
            get {
                return ResourceManager.GetString("NavigateToUserPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Appointment.
        /// </summary>
        public static string New_Appointment {
            get {
                return ResourceManager.GetString("New Appointment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NewCrop ex Role.
        /// </summary>
        public static string NewCropeRxRole {
            get {
                return ResourceManager.GetString("NewCropeRxRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No User Found.
        /// </summary>
        public static string No_User_Found {
            get {
                return ResourceManager.GetString("No User Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No changes detected.
        /// </summary>
        public static string NoChangesDetected {
            get {
                return ResourceManager.GetString("NoChangesDetected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No license found for organization: {0}. Adding free license..
        /// </summary>
        public static string NoLicenseFound {
            get {
                return ResourceManager.GetString("NoLicenseFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No members to register..
        /// </summary>
        public static string NoMembers {
            get {
                return ResourceManager.GetString("NoMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoMemberSelected.
        /// </summary>
        public static string NoMemberSelected {
            get {
                return ResourceManager.GetString("NoMemberSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Organizations Available.
        /// </summary>
        public static string NoOrganizationsAvailable {
            get {
                return ResourceManager.GetString("NoOrganizationsAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Products Available.
        /// </summary>
        public static string NoProductsAvailable {
            get {
                return ResourceManager.GetString("NoProductsAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Products Found.
        /// </summary>
        public static string NoProductsFound {
            get {
                return ResourceManager.GetString("NoProductsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0.
        /// </summary>
        public static string NoResultsCount {
            get {
                return ResourceManager.GetString("NoResultsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes:.
        /// </summary>
        public static string Notes_ {
            get {
                return ResourceManager.GetString("Notes:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoUserFound.
        /// </summary>
        public static string NoUserFound {
            get {
                return ResourceManager.GetString("NoUserFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoUsersFound.
        /// </summary>
        public static string NoUsersFound {
            get {
                return ResourceManager.GetString("NoUsersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Occupation.
        /// </summary>
        public static string Occupation {
            get {
                return ResourceManager.GetString("Occupation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office Visits.
        /// </summary>
        public static string Office_Visits {
            get {
                return ResourceManager.GetString("Office Visits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OfficeLocation.
        /// </summary>
        public static string OfficeLocation {
            get {
                return ResourceManager.GetString("OfficeLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OfficeVisit.
        /// </summary>
        public static string OfficeVisit {
            get {
                return ResourceManager.GetString("OfficeVisit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ok.
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order Set.
        /// </summary>
        public static string Order_Set {
            get {
                return ResourceManager.GetString("Order Set", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OrganizationAlreadyExists.
        /// </summary>
        public static string OrganizationAlreadyExists {
            get {
                return ResourceManager.GetString("OrganizationAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OrganizationDeletedSuccessfully.
        /// </summary>
        public static string OrganizationDeletedSuccessfully {
            get {
                return ResourceManager.GetString("OrganizationDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organization Product Access Heading.
        /// </summary>
        public static string OrganizationProductAccessHeading {
            get {
                return ResourceManager.GetString("OrganizationProductAccessHeading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OrganizationRegistered.
        /// </summary>
        public static string OrganizationRegistered {
            get {
                return ResourceManager.GetString("OrganizationRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OrganizationRegistration.
        /// </summary>
        public static string OrganizationRegistration {
            get {
                return ResourceManager.GetString("OrganizationRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizations.
        /// </summary>
        public static string Organizations {
            get {
                return ResourceManager.GetString("Organizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to P/R.
        /// </summary>
        public static string P_R {
            get {
                return ResourceManager.GetString("P/R", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagePathRequired.
        /// </summary>
        public static string PagePathRequired {
            get {
                return ResourceManager.GetString("PagePathRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PagePathRequiredMessage.
        /// </summary>
        public static string PagePathRequiredMessage {
            get {
                return ResourceManager.GetString("PagePathRequiredMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PageRoleMappingDeletedSuccessfully.
        /// </summary>
        public static string PageRoleMappingDeletedSuccessfully {
            get {
                return ResourceManager.GetString("PageRoleMappingDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PageRoleMappingDeletionFailed.
        /// </summary>
        public static string PageRoleMappingDeletionFailed {
            get {
                return ResourceManager.GetString("PageRoleMappingDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PageRoleMappingUpdatedSuccessfully.
        /// </summary>
        public static string PageRoleMappingUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("PageRoleMappingUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PageRoleMappingUpdateFailed.
        /// </summary>
        public static string PageRoleMappingUpdateFailed {
            get {
                return ResourceManager.GetString("PageRoleMappingUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Health!.
        /// </summary>
        public static string PageTitle {
            get {
                return ResourceManager.GetString("PageTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string PasswordLabel {
            get {
                return ResourceManager.GetString("PasswordLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PasswordRequired.
        /// </summary>
        public static string PasswordRequired {
            get {
                return ResourceManager.GetString("PasswordRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient.
        /// </summary>
        public static string Patient {
            get {
                return ResourceManager.GetString("Patient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Address.
        /// </summary>
        public static string Patient_Address {
            get {
                return ResourceManager.GetString("Patient Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create this new patient record?.
        /// </summary>
        public static string Patient_Create {
            get {
                return ResourceManager.GetString("Patient.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Docs.
        /// </summary>
        public static string Patient_Docs {
            get {
                return ResourceManager.GetString("Patient Docs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Hub.
        /// </summary>
        public static string Patient_Hub {
            get {
                return ResourceManager.GetString("Patient Hub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Name.
        /// </summary>
        public static string Patient_Name {
            get {
                return ResourceManager.GetString("Patient Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to update this Patient Details?.
        /// </summary>
        public static string Patient_Update {
            get {
                return ResourceManager.GetString("Patient.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient deleted successfully.
        /// </summary>
        public static string Patientdelete {
            get {
                return ResourceManager.GetString("Patientdelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Image removed successfully.
        /// </summary>
        public static string PatientImagesuccessful {
            get {
                return ResourceManager.GetString("PatientImagesuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Menu Role.
        /// </summary>
        public static string PatientMenuRole {
            get {
                return ResourceManager.GetString("PatientMenuRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to string.Empty.
        /// </summary>
        public static string PatientName {
            get {
                return ResourceManager.GetString("PatientName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patient Photo Uploaded.
        /// </summary>
        public static string PatientPhotoUploaded {
            get {
                return ResourceManager.GetString("PatientPhotoUploaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patients.
        /// </summary>
        public static string Patients {
            get {
                return ResourceManager.GetString("Patients", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /patients/.
        /// </summary>
        public static string PatientsPagePath {
            get {
                return ResourceManager.GetString("PatientsPagePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /patients/{0}.
        /// </summary>
        public static string PatientsPathTemplate {
            get {
                return ResourceManager.GetString("PatientsPathTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pause.
        /// </summary>
        public static string Pause {
            get {
                return ResourceManager.GetString("Pause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PCP.
        /// </summary>
        public static string PCP {
            get {
                return ResourceManager.GetString("PCP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pharmacy Address.
        /// </summary>
        public static string Pharmacy_Address {
            get {
                return ResourceManager.GetString("Pharmacy Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pharmacy Name.
        /// </summary>
        public static string Pharmacy_Name {
            get {
                return ResourceManager.GetString("Pharmacy Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Number.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Therapy.
        /// </summary>
        public static string Physical_Therapy {
            get {
                return ResourceManager.GetString("Physical Therapy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Therapy Diagnosis.
        /// </summary>
        public static string Physical_Therapy_Diagnosis {
            get {
                return ResourceManager.GetString("Physical Therapy Diagnosis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Therapy Program.
        /// </summary>
        public static string Physical_Therapy_Program {
            get {
                return ResourceManager.GetString("Physical Therapy Program", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plan Name.
        /// </summary>
        public static string PlanName {
            get {
                return ResourceManager.GetString("PlanName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to play_arrow.
        /// </summary>
        public static string Play {
            get {
                return ResourceManager.GetString("Play", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Country.
        /// </summary>
        public static string Please_Enter_Country {
            get {
                return ResourceManager.GetString("Please Enter Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Fill All Required Fields.
        /// </summary>
        public static string Please_Fill_All_Required_Fields {
            get {
                return ResourceManager.GetString("Please Fill All Required Fields", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please fill all required fields..
        /// </summary>
        public static string Please_fill_all_required_fields_ {
            get {
                return ResourceManager.GetString("Please fill all required fields.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy.
        /// </summary>
        public static string Policy {
            get {
                return ResourceManager.GetString("Policy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Number.
        /// </summary>
        public static string PolicyNumber {
            get {
                return ResourceManager.GetString("PolicyNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code.
        /// </summary>
        public static string PostalCode {
            get {
                return ResourceManager.GetString("PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while registering members..
        /// </summary>
        public static string PostLogError {
            get {
                return ResourceManager.GetString("PostLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Practice.
        /// </summary>
        public static string Practice {
            get {
                return ResourceManager.GetString("Practice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferred Name.
        /// </summary>
        public static string PreferredName {
            get {
                return ResourceManager.GetString("PreferredName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prescription Medication.
        /// </summary>
        public static string Prescription_Medication {
            get {
                return ResourceManager.GetString("Prescription Medication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous Names.
        /// </summary>
        public static string PreviousNames {
            get {
                return ResourceManager.GetString("PreviousNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Insurance Provider.
        /// </summary>
        public static string PrimaryInsuranceProvider {
            get {
                return ResourceManager.GetString("PrimaryInsuranceProvider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        public static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procedure Alert Records.
        /// </summary>
        public static string Procedure_Alert_Records {
            get {
                return ResourceManager.GetString("Procedure Alert Records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procedure Alerts.
        /// </summary>
        public static string Procedure_Alerts {
            get {
                return ResourceManager.GetString("Procedure Alerts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procedures.
        /// </summary>
        public static string Procedures {
            get {
                return ResourceManager.GetString("Procedures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRODUCT.
        /// </summary>
        public static string PRODUCT {
            get {
                return ResourceManager.GetString("PRODUCT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to productId.
        /// </summary>
        public static string productId {
            get {
                return ResourceManager.GetString("productId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Name.
        /// </summary>
        public static string ProductName {
            get {
                return ResourceManager.GetString("ProductName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to retrieve products
        ///.
        /// </summary>
        public static string ProductRetrievalFailure {
            get {
                return ResourceManager.GetString("ProductRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to null.
        /// </summary>
        public static string Provider {
            get {
                return ResourceManager.GetString("Provider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Provider:.
        /// </summary>
        public static string Provider_ {
            get {
                return ResourceManager.GetString("Provider:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        public static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to R-1111.
        /// </summary>
        public static string R_1111 {
            get {
                return ResourceManager.GetString("R-1111", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Race.
        /// </summary>
        public static string Race {
            get {
                return ResourceManager.GetString("Race", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string <NAME_EMAIL>.
        /// </summary>
        public static string Ramesh_mail {
            get {
                return ResourceManager.GetString("Ramesh_mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&amp;*().
        /// </summary>
        public static string RandomPasswordChars {
            get {
                return ResourceManager.GetString("RandomPasswordChars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reaction must contain alphabets only..
        /// </summary>
        public static string ReactionInvalid {
            get {
                return ResourceManager.GetString("ReactionInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reaction is required..
        /// </summary>
        public static string ReactionRequired {
            get {
                return ResourceManager.GetString("ReactionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        public static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason Deceased.
        /// </summary>
        public static string ReasonDeceased {
            get {
                return ResourceManager.GetString("ReasonDeceased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Changes Successfully.
        /// </summary>
        public static string RecordSaved {
            get {
                return ResourceManager.GetString("RecordSaved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RecordsUrl.
        /// </summary>
        public static string RecordsUrl {
            get {
                return ResourceManager.GetString("RecordsUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RedirectUrl.
        /// </summary>
        public static string RedirectUrl {
            get {
                return ResourceManager.GetString("RedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referral From.
        /// </summary>
        public static string Referral_From {
            get {
                return ResourceManager.GetString("Referral From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referral Outgoing.
        /// </summary>
        public static string Referral_Outgoing {
            get {
                return ResourceManager.GetString("Referral Outgoing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referral Reason.
        /// </summary>
        public static string Referral_Reason {
            get {
                return ResourceManager.GetString("Referral Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referral To.
        /// </summary>
        public static string Referral_To {
            get {
                return ResourceManager.GetString("Referral To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ReferralOutgoing.
        /// </summary>
        public static string ReferralOutgoing {
            get {
                return ResourceManager.GetString("ReferralOutgoing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Referral Source.
        /// </summary>
        public static string ReferralSource {
            get {
                return ResourceManager.GetString("ReferralSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refill Details.
        /// </summary>
        public static string Refill_Details {
            get {
                return ResourceManager.GetString("Refill Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RefillRx.
        /// </summary>
        public static string RefillRx {
            get {
                return ResourceManager.GetString("RefillRx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration Failed.
        /// </summary>
        public static string RegistrationFailed {
            get {
                return ResourceManager.GetString("RegistrationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relationship.
        /// </summary>
        public static string Relationship {
            get {
                return ResourceManager.GetString("Relationship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Religion.
        /// </summary>
        public static string Religion {
            get {
                return ResourceManager.GetString("Religion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Product Access.
        /// </summary>
        public static string RemoveProductAccess {
            get {
                return ResourceManager.GetString("RemoveProductAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Required.
        /// </summary>
        public static string Res1Email {
            get {
                return ResourceManager.GetString("Res1Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field Required.
        /// </summary>
        public static string Res1Field {
            get {
                return ResourceManager.GetString("Res1Field", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name Required.
        /// </summary>
        public static string Res1FName {
            get {
                return ResourceManager.GetString("Res1FName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name Required.
        /// </summary>
        public static string Res1LName {
            get {
                return ResourceManager.GetString("Res1LName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code Required.
        /// </summary>
        public static string Res1PCode {
            get {
                return ResourceManager.GetString("Res1PCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username Required.
        /// </summary>
        public static string Res1UName {
            get {
                return ResourceManager.GetString("Res1UName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Email.
        /// </summary>
        public static string Res2Email {
            get {
                return ResourceManager.GetString("Res2Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field Invalid.
        /// </summary>
        public static string Res2Field {
            get {
                return ResourceManager.GetString("Res2Field", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name Invalid.
        /// </summary>
        public static string Res2FName {
            get {
                return ResourceManager.GetString("Res2FName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name Invalid.
        /// </summary>
        public static string Res2LName {
            get {
                return ResourceManager.GetString("Res2LName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code Invalid.
        /// </summary>
        public static string Res2PCode {
            get {
                return ResourceManager.GetString("Res2PCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username Invalid.
        /// </summary>
        public static string Res2UName {
            get {
                return ResourceManager.GetString("Res2UName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ReScheduled.
        /// </summary>
        public static string ReScheduled {
            get {
                return ResourceManager.GetString("ReScheduled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Response.
        /// </summary>
        public static string Response {
            get {
                return ResourceManager.GetString("Response", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ret-Rx.
        /// </summary>
        public static string Ret_Rx {
            get {
                return ResourceManager.GetString("Ret-Rx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review Of System.
        /// </summary>
        public static string ReviewOfSystem {
            get {
                return ResourceManager.GetString("ReviewOfSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role.
        /// </summary>
        public static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role Selection is required!.
        /// </summary>
        public static string Role_Selection_is_required_ {
            get {
                return ResourceManager.GetString("Role Selection is required!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoleAlreadyExists.
        /// </summary>
        public static string RoleAlreadyExists {
            get {
                return ResourceManager.GetString("RoleAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoleDeletedSuccessfully.
        /// </summary>
        public static string RoleDeletedSuccessfully {
            get {
                return ResourceManager.GetString("RoleDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoleDeletionFailed.
        /// </summary>
        public static string RoleDeletionFailed {
            get {
                return ResourceManager.GetString("RoleDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoleRegistered.
        /// </summary>
        public static string RoleRegistered {
            get {
                return ResourceManager.GetString("RoleRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoleRegistrationError.
        /// </summary>
        public static string RoleRegistrationError {
            get {
                return ResourceManager.GetString("RoleRegistrationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roles.
        /// </summary>
        public static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RolesNotFoundMessage.
        /// </summary>
        public static string RolesNotFoundMessage {
            get {
                return ResourceManager.GetString("RolesNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Room Number.
        /// </summary>
        public static string Room_Number {
            get {
                return ResourceManager.GetString("Room Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rx Eligibility.
        /// </summary>
        public static string Rx_Eligibility {
            get {
                return ResourceManager.GetString("Rx Eligibility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rx External History.
        /// </summary>
        public static string Rx_External_History {
            get {
                return ResourceManager.GetString("Rx External History", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RxNorm.
        /// </summary>
        public static string RxNorm {
            get {
                return ResourceManager.GetString("RxNorm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error in getting drug names.
        /// </summary>
        public static string RxNormError1 {
            get {
                return ResourceManager.GetString("RxNormError1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save as Pdf.
        /// </summary>
        public static string Save_as_Pdf {
            get {
                return ResourceManager.GetString("Save as Pdf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string SaveChanges {
            get {
                return ResourceManager.GetString("SaveChanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User details saved successfully..
        /// </summary>
        public static string Saved {
            get {
                return ResourceManager.GetString("Saved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Assessments.
        /// </summary>
        public static string Search_Assessments {
            get {
                return ResourceManager.GetString("Search Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Brand Names.
        /// </summary>
        public static string Search_Brand_Names {
            get {
                return ResourceManager.GetString("Search Brand Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Therapy.
        /// </summary>
        public static string Search_Therapy {
            get {
                return ResourceManager.GetString("Search Therapy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchBy:.
        /// </summary>
        public static string SearchBy_ {
            get {
                return ResourceManager.GetString("SearchBy:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchByEmail.
        /// </summary>
        public static string SearchByEmail {
            get {
                return ResourceManager.GetString("SearchByEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to null.
        /// </summary>
        public static string SearchCriteria {
            get {
                return ResourceManager.GetString("SearchCriteria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Drug Names.
        /// </summary>
        public static string SearchDN {
            get {
                return ResourceManager.GetString("SearchDN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Organization.
        /// </summary>
        public static string SearchOrganization {
            get {
                return ResourceManager.GetString("SearchOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Select Organization.
        /// </summary>
        public static string SearchSelectOrganization {
            get {
                return ResourceManager.GetString("SearchSelectOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchUser.
        /// </summary>
        public static string SearchUser {
            get {
                return ResourceManager.GetString("SearchUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchUser:.
        /// </summary>
        public static string SearchUser_ {
            get {
                return ResourceManager.GetString("SearchUser:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search by Username.
        /// </summary>
        public static string SearchUsername {
            get {
                return ResourceManager.GetString("SearchUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchUsers.
        /// </summary>
        public static string SearchUsers {
            get {
                return ResourceManager.GetString("SearchUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Appointment Date.
        /// </summary>
        public static string Select_Appointment_Date {
            get {
                return ResourceManager.GetString("Select Appointment Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select End Time.
        /// </summary>
        public static string Select_End_Time {
            get {
                return ResourceManager.GetString("Select End Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Facility.
        /// </summary>
        public static string Select_Facility {
            get {
                return ResourceManager.GetString("Select Facility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Provider.
        /// </summary>
        public static string Select_Provider {
            get {
                return ResourceManager.GetString("Select Provider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Start Time.
        /// </summary>
        public static string Select_Start_Time {
            get {
                return ResourceManager.GetString("Select Start Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Strength/Dosage.
        /// </summary>
        public static string Select_Strength_Dosage {
            get {
                return ResourceManager.GetString("Select Strength/Dosage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Visit Status.
        /// </summary>
        public static string Select_Visit_Status {
            get {
                return ResourceManager.GetString("Select Visit Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Visit Type.
        /// </summary>
        public static string Select_Visit_Type {
            get {
                return ResourceManager.GetString("Select Visit Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Medication.
        /// </summary>
        public static string Selected_Medication {
            get {
                return ResourceManager.GetString("Selected Medication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Route.
        /// </summary>
        public static string Selected_Route {
            get {
                return ResourceManager.GetString("Selected Route", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Take.
        /// </summary>
        public static string Selected_Take {
            get {
                return ResourceManager.GetString("Selected Take", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectFacility.
        /// </summary>
        public static string SelectFacility {
            get {
                return ResourceManager.GetString("SelectFacility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Organization.
        /// </summary>
        public static string SelectOrganization {
            get {
                return ResourceManager.GetString("SelectOrganization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Product To Add.
        /// </summary>
        public static string SelectProductToAdd {
            get {
                return ResourceManager.GetString("SelectProductToAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectProvider.
        /// </summary>
        public static string SelectProvider {
            get {
                return ResourceManager.GetString("SelectProvider", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Provider Type.
        /// </summary>
        public static string SelectProviderType {
            get {
                return ResourceManager.GetString("SelectProviderType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectRole.
        /// </summary>
        public static string SelectRole {
            get {
                return ResourceManager.GetString("SelectRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectSingleUser.
        /// </summary>
        public static string SelectSingleUser {
            get {
                return ResourceManager.GetString("SelectSingleUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectVisitStatus.
        /// </summary>
        public static string SelectVisitStatus {
            get {
                return ResourceManager.GetString("SelectVisitStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectVisitType.
        /// </summary>
        public static string SelectVisitType {
            get {
                return ResourceManager.GetString("SelectVisitType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send E Prescription.
        /// </summary>
        public static string Send_E_Prescription {
            get {
                return ResourceManager.GetString("Send E Prescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sex.
        /// </summary>
        public static string Sex {
            get {
                return ResourceManager.GetString("Sex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sexual Orientation.
        /// </summary>
        public static string SexualOrientation {
            get {
                return ResourceManager.GetString("SexualOrientation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Term Goals.
        /// </summary>
        public static string Short_Term_Goals {
            get {
                return ResourceManager.GetString("Short Term Goals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Preview RX.
        /// </summary>
        public static string Show_Preview_RX {
            get {
                return ResourceManager.GetString("Show Preview RX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign Out.
        /// </summary>
        public static string SignOut {
            get {
                return ResourceManager.GetString("SignOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specialist Referral.
        /// </summary>
        public static string Specialist_Referral {
            get {
                return ResourceManager.GetString("Specialist Referral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN.
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateTime.MinValue.
        /// </summary>
        public static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StartTime:.
        /// </summary>
        public static string StartTime_ {
            get {
                return ResourceManager.GetString("StartTime:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stats.
        /// </summary>
        public static string Stats {
            get {
                return ResourceManager.GetString("Stats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stats Details.
        /// </summary>
        public static string StatsDetails {
            get {
                return ResourceManager.GetString("StatsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to stop_circle.
        /// </summary>
        public static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string StreetAddress {
            get {
                return ResourceManager.GetString("StreetAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit.
        /// </summary>
        public static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber.
        /// </summary>
        public static string Subscriber {
            get {
                return ResourceManager.GetString("Subscriber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Address Line 1.
        /// </summary>
        public static string SubscriberAddressLine1 {
            get {
                return ResourceManager.GetString("SubscriberAddressLine1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Address Line 2.
        /// </summary>
        public static string SubscriberAddressLine2 {
            get {
                return ResourceManager.GetString("SubscriberAddressLine2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber City.
        /// </summary>
        public static string SubscriberCity {
            get {
                return ResourceManager.GetString("SubscriberCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Country.
        /// </summary>
        public static string SubscriberCountry {
            get {
                return ResourceManager.GetString("SubscriberCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Employer.
        /// </summary>
        public static string SubscriberEmployer {
            get {
                return ResourceManager.GetString("SubscriberEmployer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Phone.
        /// </summary>
        public static string SubscriberPhone {
            get {
                return ResourceManager.GetString("SubscriberPhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber State.
        /// </summary>
        public static string SubscriberState {
            get {
                return ResourceManager.GetString("SubscriberState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subscriber Zip Code.
        /// </summary>
        public static string SubscriberZipCode {
            get {
                return ResourceManager.GetString("SubscriberZipCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Members registered successfully..
        /// </summary>
        public static string SuccessfulRegistration {
            get {
                return ResourceManager.GetString("SuccessfulRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request successful.
        /// </summary>
        public static string SuccessfulRequest {
            get {
                return ResourceManager.GetString("SuccessfulRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you have any questions, feel free to reach out to our support <NAME_EMAIL>..
        /// </summary>
        public static string SupportMessage {
            get {
                return ResourceManager.GetString("SupportMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string Surname {
            get {
                return ResourceManager.GetString("Surname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to surname.
        /// </summary>
        public static string SurName1 {
            get {
                return ResourceManager.GetString("SurName1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tasks.
        /// </summary>
        public static string Tasks {
            get {
                return ResourceManager.GetString("Tasks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Federal Tax ID must be exactly 9 digits..
        /// </summary>
        public static string TaxIdInvalidFormat {
            get {
                return ResourceManager.GetString("TaxIdInvalidFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Federal Tax ID is required!.
        /// </summary>
        public static string TaxIdRequired {
            get {
                return ResourceManager.GetString("TaxIdRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tests.
        /// </summary>
        public static string Tests {
            get {
                return ResourceManager.GetString("Tests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya Health.
        /// </summary>
        public static string TeyaHealth {
            get {
                return ResourceManager.GetString("TeyaHealth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya Recorder.
        /// </summary>
        public static string TeyaRecorder {
            get {
                return ResourceManager.GetString("TeyaRecorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for choosing Teya Health!.
        /// </summary>
        public static string ThankYouMessage {
            get {
                return ResourceManager.GetString("ThankYouMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Therapeutic Interventions.
        /// </summary>
        public static string Therapeutic_Interventions {
            get {
                return ResourceManager.GetString("Therapeutic Interventions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TherapeuticInterventionsListCodeRetrievalFailure.
        /// </summary>
        public static string TherapeuticInterventionsListCodeRetrievalFailure {
            get {
                return ResourceManager.GetString("TherapeuticInterventionsListCodeRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Therapy Assessment.
        /// </summary>
        public static string Therapy_Assessment {
            get {
                return ResourceManager.GetString("Therapy Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Therapy Type.
        /// </summary>
        public static string Therapy_Type {
            get {
                return ResourceManager.GetString("Therapy Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time.
        /// </summary>
        public static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Health!.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Treatment Plan.
        /// </summary>
        public static string Treatment_Plan {
            get {
                return ResourceManager.GetString("Treatment Plan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type must be alphanumeric or contains hyphens..
        /// </summary>
        public static string TypeInvalid {
            get {
                return ResourceManager.GetString("TypeInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type is required..
        /// </summary>
        public static string TypeRequired {
            get {
                return ResourceManager.GetString("TypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string Unknown {
            get {
                return ResourceManager.GetString("Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated Date.
        /// </summary>
        public static string Updated_Date {
            get {
                return ResourceManager.GetString("Updated Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated Date.
        /// </summary>
        public static string UpdatedDate {
            get {
                return ResourceManager.GetString("UpdatedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error updating profile..
        /// </summary>
        public static string UpdateError {
            get {
                return ResourceManager.GetString("UpdateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update user profile..
        /// </summary>
        public static string UpdateFailed {
            get {
                return ResourceManager.GetString("UpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log not updated.
        /// </summary>
        public static string UpdateLogError {
            get {
                return ResourceManager.GetString("UpdateLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Successful.
        /// </summary>
        public static string UpdateSuccessful {
            get {
                return ResourceManager.GetString("UpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /updateuser.
        /// </summary>
        public static string UpdateUserPath {
            get {
                return ResourceManager.GetString("UpdateUserPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPIN must be 5 letters followed by 1 digit..
        /// </summary>
        public static string UPINInvalidFormat {
            get {
                return ResourceManager.GetString("UPINInvalidFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPIN is required!.
        /// </summary>
        public static string UPINRequired {
            get {
                return ResourceManager.GetString("UPINRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Patient Photo.
        /// </summary>
        public static string UploadPatientPhoto {
            get {
                return ResourceManager.GetString("UploadPatientPhoto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User profile.
        /// </summary>
        public static string User_profile {
            get {
                return ResourceManager.GetString("User profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserAlreadyExists.
        /// </summary>
        public static string UserAlreadyExists {
            get {
                return ResourceManager.GetString("UserAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreated.
        /// </summary>
        public static string UserCreated {
            get {
                return ResourceManager.GetString("UserCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreatedSuccess.
        /// </summary>
        public static string UserCreatedSuccess {
            get {
                return ResourceManager.GetString("UserCreatedSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreationError.
        /// </summary>
        public static string UserCreationError {
            get {
                return ResourceManager.GetString("UserCreationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreationFailed.
        /// </summary>
        public static string UserCreationFailed {
            get {
                return ResourceManager.GetString("UserCreationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserDataNotLoaded.
        /// </summary>
        public static string UserDataNotLoaded {
            get {
                return ResourceManager.GetString("UserDataNotLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserEmailAddress.
        /// </summary>
        public static string UserEmailAddress {
            get {
                return ResourceManager.GetString("UserEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserID.
        /// </summary>
        public static string UserID {
            get {
                return ResourceManager.GetString("UserID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInformationForm.
        /// </summary>
        public static string UserInformationForm {
            get {
                return ResourceManager.GetString("UserInformationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInformationFormHeader.
        /// </summary>
        public static string UserInformationFormHeader {
            get {
                return ResourceManager.GetString("UserInformationFormHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInvitedSuccessfully.
        /// </summary>
        public static string UserInvitedSuccessfully {
            get {
                return ResourceManager.GetString("UserInvitedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Management.
        /// </summary>
        public static string UserManagement {
            get {
                return ResourceManager.GetString("UserManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Management.
        /// </summary>
        public static string UserManagementHeading {
            get {
                return ResourceManager.GetString("UserManagementHeading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /usermanagement.
        /// </summary>
        public static string UserManagementRoute {
            get {
                return ResourceManager.GetString("UserManagementRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Name.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username is required!.
        /// </summary>
        public static string Username_is_required_ {
            get {
                return ResourceManager.GetString("Username is required!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to preferred_username.
        /// </summary>
        public static string UsernameClaim {
            get {
                return ResourceManager.GetString("UsernameClaim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username can only contain letters, numbers, dots, underscores, and hyphens..
        /// </summary>
        public static string UsernameInvalidCharacters {
            get {
                return ResourceManager.GetString("UsernameInvalidCharacters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username must be at least 5 characters long..
        /// </summary>
        public static string UsernameMinLength {
            get {
                return ResourceManager.GetString("UsernameMinLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username cannot contain spaces..
        /// </summary>
        public static string UsernameNoSpaces {
            get {
                return ResourceManager.GetString("UsernameNoSpaces", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UsernameRequired.
        /// </summary>
        public static string UsernameRequired {
            get {
                return ResourceManager.GetString("UsernameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserPrincipalName.
        /// </summary>
        public static string UserPrincipalName {
            get {
                return ResourceManager.GetString("UserPrincipalName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User profile updated successfully..
        /// </summary>
        public static string UserProfileUpdated {
            get {
                return ResourceManager.GetString("UserProfileUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Type.
        /// </summary>
        public static string UserType {
            get {
                return ResourceManager.GetString("UserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Future Dates are not allowed..
        /// </summary>
        public static string ValidateCreateDate {
            get {
                return ResourceManager.GetString("ValidateCreateDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        public static string View {
            get {
                return ResourceManager.GetString("View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Log.
        /// </summary>
        public static string View_Log {
            get {
                return ResourceManager.GetString("View Log", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visit Status.
        /// </summary>
        public static string Visit_Status {
            get {
                return ResourceManager.GetString("Visit Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visit Type.
        /// </summary>
        public static string Visit_Type {
            get {
                return ResourceManager.GetString("Visit Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VisitName.
        /// </summary>
        public static string VisitName {
            get {
                return ResourceManager.GetString("VisitName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to null.
        /// </summary>
        public static string VisitStatus {
            get {
                return ResourceManager.GetString("VisitStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VisitStatus:.
        /// </summary>
        public static string VisitStatus_ {
            get {
                return ResourceManager.GetString("VisitStatus:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to null.
        /// </summary>
        public static string VisitType {
            get {
                return ResourceManager.GetString("VisitType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VisitType:.
        /// </summary>
        public static string VisitType_ {
            get {
                return ResourceManager.GetString("VisitType:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web Reference.
        /// </summary>
        public static string Web_Reference {
            get {
                return ResourceManager.GetString("Web Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web View.
        /// </summary>
        public static string Web_View {
            get {
                return ResourceManager.GetString("Web View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight.
        /// </summary>
        public static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to welcome to teya.
        /// </summary>
        public static string wel {
            get {
                return ResourceManager.GetString("wel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Web App!.
        /// </summary>
        public static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Health!.
        /// </summary>
        public static string WelcomeHeading {
            get {
                return ResourceManager.GetString("WelcomeHeading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who.
        /// </summary>
        public static string Who {
            get {
                return ResourceManager.GetString("Who", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
