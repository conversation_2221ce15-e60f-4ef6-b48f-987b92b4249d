﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ISocialHistoryService
    {
        Task<List<PatientSocialHistory>> GetAllByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<PatientSocialHistory>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddHistoryListAsync(List<PatientSocialHistory> histories, Guid? OrgID, bool Subscription);
        Task UpdateHistoryListAsync(List<PatientSocialHistory> histories, Guid? OrgID, bool Subscription);
    }
}
