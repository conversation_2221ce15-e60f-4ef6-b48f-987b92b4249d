﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Core;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models.CallRecords;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class OrderSetService : IOrderSetService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _AlertService;
        private readonly IStringLocalizer<OrderSetService> _localizer;
        private readonly ILogger<OrderSetService> _logger;

        public OrderSetService(HttpClient httpClient, IStringLocalizer<OrderSetService> localizer, ILogger<OrderSetService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _AlertService = Environment.GetEnvironmentVariable("AlertsServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<CompleteOrderSet>> GetAllOrderSetAsync()
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<CompleteOrderSet>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllOrderSet"]);
                throw;
            }
        }

        public async Task<CompleteOrderSet> GetOrderSetByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet/{id}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<CompleteOrderSet>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetOrderSetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrderSetById"], id);
                throw;
            }
        }

        public async Task<IEnumerable<CompleteOrderSet>> GetOrderSetByNameAsync(string name)
        {
            try
            {
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet/search?name={Uri.EscapeDataString(name)}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<IEnumerable<CompleteOrderSet>>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetOrderSetByNameFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingOrderSetByName"], name);
                throw;
            }
        }

        public async Task AddOrderSetAsync(CompleteOrderSet orderSet)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = JsonSerializer.Serialize(new List<CompleteOrderSet> { orderSet });
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["OrderSetAddedSuccessfully"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddOrderSetFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingOrderSet"]);
                throw;
            }
        }

        public async Task UpdateOrderSetAsync(CompleteOrderSet orderSet)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet/{orderSet.Id}";
                var bodyContent = JsonSerializer.Serialize(orderSet);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["CountryUpdatedSuccessfully"], orderSet.Id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["OrderSetUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingOrderSet"]);
                throw;
            }
        }

        public async Task DeleteOrderSetByIdAsync(Guid id)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_AlertService}/api/CompleteOrderSet/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["OrderSetDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["OrderSetDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingOrderSet"], id);
                throw;
            }
        }
    }
}
