﻿

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace TeyaUIModels.Model
{
    public class GynHistoryDTO : IModel
    {
        public Guid gynId { get; set; }
        public Guid PatientId { get; set; }
        public string Symptoms { get; set; }
        public string Notes { get; set; }
        public DateTime DateOfHistory { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid? PcpId { get; set; }
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;
    }
}
