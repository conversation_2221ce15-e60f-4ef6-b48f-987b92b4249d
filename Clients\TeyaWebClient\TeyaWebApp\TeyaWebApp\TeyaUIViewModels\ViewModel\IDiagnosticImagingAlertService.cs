﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDiagnosticImagingAlertService
    {
        Task<List<DiagnosticImagingAlerts>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImagingAlerts>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddDIAlertsAsync(List<DiagnosticImagingAlerts> diAlerts, Guid? OrgID, bool Subscription);
        Task UpdateDIAlertAsync(DiagnosticImagingAlerts diAlert, Guid? OrgID, bool Subscription);
        Task UpdateDIAlertsListAsync(List<DiagnosticImagingAlerts> diAlerts, Guid? OrgID, bool Subscription);
        Task DeleteDIAlertByEntityAsync(DiagnosticImagingAlerts diAlert, Guid? OrgID, bool Subscription);
    }
}