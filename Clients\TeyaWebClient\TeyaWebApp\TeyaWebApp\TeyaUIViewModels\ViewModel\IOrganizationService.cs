﻿using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;

namespace TeyaUIModels.ViewModel
{
    public interface IOrganizationService
    {
        Task<Organization> RegisterOrganizationsAsync(Organization organization);
        Task<Organization> GetOrganizationByIdAsync(Guid OrganizationId);
        Task<List<Organization>> GetAllOrganizationsAsync();
        Task DeleteOrganizationByIdAsync(Guid OrganizationId);
        Task UpdateOrganizationByIdAsync(Guid OrganizationId, Organization Organization);
        Task<List<Organization>> GetOrganizationsByNameAsync(string name);
        Task<Guid> GetOrganizationIdByNameAsync(string orgName);
    }
}
