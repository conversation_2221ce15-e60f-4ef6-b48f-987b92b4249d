﻿﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICustomLabAlertService
    {
        Task<List<CustomLabAlerts>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CustomLabAlerts>> GetActiveCustomLabAlertsByOrganizationIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddCustomLabAlertsAsync(List<CustomLabAlerts> customLabAlerts, Guid? OrgID, bool Subscription);
        Task UpdateCustomLabAlertAsync(CustomLabAlerts customLabAlert, Guid? OrgID, bool Subscription);
        Task UpdateCustomLabAlertsListAsync(List<CustomLabAlerts> customLabAlerts, Guid? OrgID, bool Subscription);
        Task DeleteCustomLabAlertByEntityAsync(CustomLabAlerts customLabAlert, Guid? OrgID, bool Subscription);
    }
}
