﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDiagnosticImagingService
    {
        Task CreateDiagnosticImagingAsync(List<DiagnosticImage> Tasks, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImage>> GetDiagnosticImagingAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateDiagnosticImagingList(List<DiagnosticImage> diagnosticImaging, Guid? OrgID, bool Subscription);
        Task<List<DiagnosticImage>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task DeleteDiagnosticImagingAsync(Guid taskId, Guid? OrgID, bool Subscription);
        Task UpdateDiagnosticImagingAsync(DiagnosticImage diagnosticImaging, Guid? OrgID, bool Subscription);
    }
}
