﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IUpToDateService
    {
        Task<UpToDate?> GetUpToDateByIdAsync(Guid id);
        Task<bool> AddUpToDateAsync(UpToDate upToDate);
        Task<bool> UpdateUpToDateAsync(Guid id, UpToDate upToDate);
        Task<bool> DeleteUpToDateAsync(Guid id);
        Task<List<UpToDate>?> GetAllUpToDatesAsync();
        Task<List<UpToDate>?> GetUpToDatesByNameAsync(string name);
    }
}
