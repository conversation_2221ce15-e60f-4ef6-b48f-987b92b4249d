﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaWebApp;

namespace TeyaUIViewModels.ViewModel
{
    public interface IObHistoryService
    {
        Task<List<ObHistoryDTO>> GetAllObHistoriesAsync(Guid? OrgID, bool Subscription);
        Task AddAsync(ObHistoryDTO obHistoryDto, Guid? OrgID, bool Subscription);
        Task UpdateObHistoryAsync(Guid id, ObHistoryDTO obHistoryDto, Guid? OrgID, bool Subscription);
        Task DeleteObHistoryAsync(Guid id, Guid? OrgID, bool Subscription);

        Task UpdateObHistoryListAsync(List<ObHistoryDTO> obHistories, Guid? OrgID, bool Subscription);
        Task<List<ObHistoryDTO>> LoadObHistoriesAsync(Guid patientId, Guid? OrgID, bool Subscription);
        Task<IEnumerable<ObHistoryDTO>> GetByPatientIdAsync(Guid patientId, Guid? OrgID, bool Subscription);
    }
}
