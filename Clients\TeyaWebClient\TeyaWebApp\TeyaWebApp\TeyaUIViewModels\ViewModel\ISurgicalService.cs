﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ISurgicalService
    {
        Task<List<Surgical>> GetSurgeriesByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddSurgeryAsync(List<Surgical> surgeries, Guid? OrgID, bool Subscription);
        Task DeletesurgeryAsync(Surgical surgery, Guid? OrgID, bool Subscription);
        Task UpdateSurgeryAsync(Surgical surgeries, Guid? OrgID, bool Subscription);
        Task<List<Surgical>> GetSurgeryByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateSurgeryListAsync(List<Surgical> surgery, Guid? OrgID, bool Subscription);
    }
}
