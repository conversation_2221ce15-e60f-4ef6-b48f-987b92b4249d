﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IOrderSetService
    {
        Task<IEnumerable<CompleteOrderSet>> GetAllOrderSetAsync();
        Task<CompleteOrderSet> GetOrderSetByIdAsync(Guid id);
        Task<IEnumerable<CompleteOrderSet>> GetOrderSetByNameAsync(string name);
        Task AddOrderSetAsync(CompleteOrderSet orderSet);
        Task UpdateOrderSetAsync(CompleteOrderSet orderSet);
        Task DeleteOrderSetByIdAsync(Guid id);
    }
}
