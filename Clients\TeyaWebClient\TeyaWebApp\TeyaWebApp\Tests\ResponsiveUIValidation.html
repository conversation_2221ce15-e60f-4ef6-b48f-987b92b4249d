<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Co-signing Component Responsive UI Validation</title>
    <style>
        /* Import the actual CSS from the components */
        @import url('../Components/Pages/CosigningComponent.razor.css');
        @import url('../Components/Pages/ReviewWindowComponent.razor.css');
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .viewport-test {
            border: 2px solid #ddd;
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .viewport-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
        
        /* Simulate different viewport sizes */
        .mobile-viewport {
            max-width: 375px;
            overflow-x: auto;
        }
        
        .tablet-viewport {
            max-width: 768px;
            overflow-x: auto;
        }
        
        .desktop-viewport {
            max-width: 1200px;
        }
        
        /* Test component styles */
        .cosigning-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin: 16px 0;
        }
        
        .workflow-steps {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .step-container {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        
        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
            min-width: 0;
        }
        
        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .step-description {
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .request-tabs {
            margin-top: 24px;
        }
        
        .tab-content {
            padding: 20px 0;
        }
        
        .data-grid-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        /* Responsive breakpoints */
        @media (max-width: 768px) {
            .cosigning-container {
                padding: 16px;
                margin: 8px 0;
            }
            
            .step-container {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }
            
            .step-number {
                align-self: center;
            }
            
            .step-title {
                font-size: 16px;
            }
            
            .workflow-steps {
                gap: 16px;
            }
        }
        
        @media (max-width: 480px) {
            .cosigning-container {
                padding: 12px;
                border-radius: 8px;
            }
            
            .step-container {
                padding: 16px;
            }
            
            .step-title {
                font-size: 14px;
            }
            
            .step-description {
                font-size: 13px;
            }
        }
        
        .validation-checklist {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 16px;
            margin: 20px 0;
        }
        
        .validation-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
        }
        
        .validation-item::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Co-signing Component Responsive UI Validation</h1>
        <p>This page validates the responsive design of the enhanced co-signing system across different viewport sizes.</p>
        
        <!-- Desktop Viewport Test -->
        <div class="viewport-test desktop-viewport">
            <div class="viewport-label">Desktop View (1200px+)</div>
            <div class="cosigning-container">
                <div class="workflow-steps">
                    <div class="step-container">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">Sign the Note</div>
                            <div class="step-description">Review and electronically sign the SOAP note to confirm its accuracy and completeness.</div>
                        </div>
                    </div>
                    <div class="step-container">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">Request Co-signing (Optional)</div>
                            <div class="step-description">After signing, you can optionally request a colleague to co-sign for additional validation.</div>
                        </div>
                    </div>
                </div>
                <div class="request-tabs">
                    <div class="tab-content">
                        <div class="data-grid-container">
                            <p style="padding: 20px; margin: 0; text-align: center; color: #666;">
                                Request management tables would appear here with professional styling
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tablet Viewport Test -->
        <div class="viewport-test tablet-viewport">
            <div class="viewport-label">Tablet View (768px)</div>
            <div class="cosigning-container">
                <div class="workflow-steps">
                    <div class="step-container">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">Sign the Note</div>
                            <div class="step-description">Review and electronically sign the SOAP note.</div>
                        </div>
                    </div>
                    <div class="step-container">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">Request Co-signing (Optional)</div>
                            <div class="step-description">Request colleague co-signing after signing.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile Viewport Test -->
        <div class="viewport-test mobile-viewport">
            <div class="viewport-label">Mobile View (375px)</div>
            <div class="cosigning-container">
                <div class="workflow-steps">
                    <div class="step-container">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">Sign the Note</div>
                            <div class="step-description">Review and sign the note.</div>
                        </div>
                    </div>
                    <div class="step-container">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">Request Co-signing</div>
                            <div class="step-description">Optional co-signing request.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Validation Checklist -->
        <div class="validation-checklist">
            <h3>UI Validation Checklist</h3>
            <div class="validation-item">Professional styling matches Notes page design</div>
            <div class="validation-item">Segoe UI font family consistently applied</div>
            <div class="validation-item">Primary color gradients used for visual hierarchy</div>
            <div class="validation-item">Step-based workflow clearly visible</div>
            <div class="validation-item">Responsive design works on mobile (375px)</div>
            <div class="validation-item">Responsive design works on tablet (768px)</div>
            <div class="validation-item">Responsive design works on desktop (1200px+)</div>
            <div class="validation-item">Proper spacing and padding maintained</div>
            <div class="validation-item">Text remains readable at all sizes</div>
            <div class="validation-item">Interactive elements remain accessible</div>
            <div class="validation-item">GitHub-style commenting UI integrated</div>
            <div class="validation-item">Request management tables properly styled</div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
            <h3>Testing Instructions</h3>
            <ol>
                <li>Open this file in a web browser</li>
                <li>Resize the browser window to test different viewport sizes</li>
                <li>Verify that all elements remain properly aligned and readable</li>
                <li>Check that the step-based workflow is clear at all sizes</li>
                <li>Ensure text doesn't overflow containers</li>
                <li>Validate that interactive elements remain accessible</li>
                <li>Test with browser developer tools device emulation</li>
            </ol>
        </div>
    </div>
</body>
</html>
