﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class LabTestService : ILabTestsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _LabTestsUrl;
        private readonly ITokenService _tokenService;

        public LabTestService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _LabTestsUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<LabTests>> GetAllByIdAsync(Guid id)
        {
            var apiUrl = $"{_LabTestsUrl}/api/LabTests/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<LabTests>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<LabTests>> GetAllByIdAndIsActiveAsync(Guid id)
        {
            var apiUrl = $"{_LabTestsUrl}/api/LabTests/{id}/IsActive";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<LabTests>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddLabTestsAsync(List<LabTests> labTests)
        {
            var apiUrl = $"{_LabTestsUrl}/api/LabTests/AddLabTests";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(labTests);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateLabTestsAsync(LabTests labTest)
        {
            var apiUrl = $"{_LabTestsUrl}/api/LabTests/{labTest.PatientId}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(labTest);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateLabTestsListAsync(List<LabTests> labTests)
        {
            var apiUrl = $"{_LabTestsUrl}/api/LabTests/UpdateLabTestsList";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(labTests);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task DeleteLabTestsByEntityAsync(LabTests labTest)
        {
            if (labTest == null)
            {
                throw new ArgumentNullException(nameof(labTest), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_LabTestsUrl}/api/LabTests/DeleteLabTest";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(labTest);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl) { Content = content };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }
    }
}
