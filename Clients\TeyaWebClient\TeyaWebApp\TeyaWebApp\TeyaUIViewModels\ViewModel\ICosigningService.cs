﻿using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningService
    {
        Task<IEnumerable<Cosigning>> GetCosigningsByRecordId(Guid recordId);
        Task<IEnumerable<Cosigning>> GetCosigningsByRecordId(Guid recordId, Guid organizationId, bool subscription);
        Task AddCosigning(List<Cosigning> cosignings);
        Task AddCosigning(List<Cosigning> cosignings, Guid organizationId, bool subscription);
        Task UpdateCosigning(Cosigning cosigning);
        Task<Cosigning> GetCosigningStatus(Guid patientId);

    }

}