﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public class DiagnosticImagingService : IDiagnosticImagingService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<DiagnosticImagingService> _localizer;
        private readonly ILogger<DiagnosticImagingService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public DiagnosticImagingService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<DiagnosticImagingService> localizer,ILogger<DiagnosticImagingService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImage>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            List<DiagnosticImage> results = new();
            try
            {
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/{id}/isActive/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    results = await response.Content.ReadFromJsonAsync<List<DiagnosticImage>>() ?? new();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to fetch active diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["AddressRetrievalFailure"]);
            }

            return results;
        }


        /// <summary>
        ///  Get All Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImage>> GetDiagnosticImagingAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            List<DiagnosticImage> results = new();
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/{id}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    results = await response.Content.ReadFromJsonAsync<List<DiagnosticImage>>() ?? new();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to fetch diagnostic imaging by ID. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["MemberRetrievalFailure"]);
            }

            return results;
        }


        /// <summary>
        /// Add new DiagnosticImaging
        /// </summary>
        public async Task CreateDiagnosticImagingAsync(List<DiagnosticImage> member, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(member);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully created diagnostic imaging."]);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to create diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["TasksRetrievalFailure"]);
            }
        }


        /// <summary>
        /// Update an existing DiagnosticImaging
        /// </summary>
        public async Task UpdateDiagnosticImagingAsync(DiagnosticImage diagnosticImaging, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/{diagnosticImaging.RecordID}/{OrgID}/{Subscription}";
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(diagnosticImaging);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully updated diagnostic imaging with RecordID: {RecordID}"], diagnosticImaging.RecordID);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to update diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["ErrorUpdatingDiagnosticImaging"]);
            }
        }


        /// <summary>
        ///  Delete an existing DiagnosticImaging By Id
        /// </summary>
        public async Task DeleteDiagnosticImagingAsync(Guid memberId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/{memberId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully deleted diagnostic imaging with MemberID: {MemberID}"], memberId);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to delete diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["ErrorDeletingDiagnosticImaging"]);
            }
        }


        /// <summary>
        ///  Update an existing List of DiagnosticImaging
        /// </summary>
        public async Task UpdateDiagnosticImagingList(List<DiagnosticImage> diagnosticImagings, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImaging/updateDiagnosticImaging/{OrgID}/{Subscription}";

                var bodyContent = System.Text.Json.JsonSerializer.Serialize(diagnosticImagings);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully updated list of diagnostic imaging."]);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to update diagnostic imaging list. StatusCode: {StatusCode}, Error: {Error}"], response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["UpdateDiagnosticImagingListFailure"]);
            }
        }

    }
}