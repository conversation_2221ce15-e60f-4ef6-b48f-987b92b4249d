﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IVisionExaminationService
    {
        Task CreateVisionExaminationAsync(VisionRx visonExamination);
        Task<List<VisionRx>> GetVisionExaminationAsync(Guid id, Guid OrgID, bool Subscription);
        Task<List<VisionRx>> GetVisionExaminationByIdAsyncAndIsActive(Guid id, Guid OrgID, bool Subscription);
        Task UpdateVisionRecordsAsync(VisionRx Member);
        Task<List<VisionRx>> GetVisionRecordsAsync(Guid OrgID, bool Subscription);
        Task DeleteVisionRecordsAsync(Guid MemberId, Guid OrgID, bool Subscription);
    }
}
