﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components.Forms;

namespace TeyaUIModels.Model
{
    public class PastResult : IModel
    {
        public bool Subscription { get; set; }
        public Guid ResultId { get; set; }
        public Guid PatientId { get; set; }
        public Guid? OrganizationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public string? OrderName { get; set; }
        public Guid PCPId { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? ResultDate { get; set; }
        public string? OrderType { get; set; }
        public string? OrderBy { get; set; }
        public string? ViewResults { get; set; }
        public bool IsActive { get; set; }
    }
}
