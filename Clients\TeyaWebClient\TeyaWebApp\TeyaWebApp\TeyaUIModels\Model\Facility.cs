﻿using System;
using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class Facility : IModel
    {
        public Guid FacilityId { get; set; }

        [Required(ErrorMessage = "Facility Name is required")]
        [StringLength(100, ErrorMessage = "Facility Name cannot exceed 100 characters")]
        public string FacilityName { get; set; }

        [StringLength(300, ErrorMessage = "Street Name cannot exceed 300 characters")]
        public string? StreetName { get; set; }

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [StringLength(100, ErrorMessage = "State cannot exceed 100 characters")]
        public string? State { get; set; }

        [RegularExpression(@"^\d{5}(-\d{4})?$", ErrorMessage = "Invalid Zip Code format")]
        public string? Zipcode { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string? Country { get; set; }

        [Required(ErrorMessage = "Created Date is required")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedBy { get; set; }
        public bool IsActive { get; set; } = true;

        [Required(ErrorMessage = "Organization ID is required")]
        public Guid OrganizationId { get; set; }
        public bool Subscription { get; set; }
    }
}
