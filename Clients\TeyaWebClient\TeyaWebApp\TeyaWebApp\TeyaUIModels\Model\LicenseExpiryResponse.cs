﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class LicenseExpiryResponse : IModel
    {
        public bool Expired { get; set; }
        public string MessageDetail { get; set; }
    }

    public class MessageDetail
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public bool ResourceNotFound { get; set; }
        public string SearchedLocation { get; set; }
    }
}
