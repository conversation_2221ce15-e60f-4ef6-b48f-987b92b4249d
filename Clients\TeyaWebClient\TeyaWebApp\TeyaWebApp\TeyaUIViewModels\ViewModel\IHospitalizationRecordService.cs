﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IHospitalizationRecordService
    {
        Task CreateHospitalizationRecordAsync(List<HospitalizationRecord> hospitalizationRecord, Guid? OrgID, bool Subscription);
        Task<List<HospitalizationRecord>> GetHospitalizationRecordAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateHospitalizationRecordList(List<HospitalizationRecord> hospitalizationRecords, Guid? OrgID, bool Subscription);
        Task<List<HospitalizationRecord>> GetHospitalizationRecordByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task DeleteHospitalizationRecordAsync(Guid taskId, Guid? OrgID, bool Subscription);
        Task UpdateHospitalizationRecordAsync(HospitalizationRecord hospitalizationRecord, Guid? OrgID, bool Subscription);
    }
}
