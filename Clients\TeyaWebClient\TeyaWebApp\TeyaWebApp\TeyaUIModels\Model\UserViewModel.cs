﻿namespace TeyaUIModels.Model
{
    public class UserViewModel
    {
        public string? id { get; set; }
        public string? displayName { get; set; }
        public string? userPrincipalName { get; set; }
        public string? givenName { get; set; }
        public string? surname { get; set; }
        public string? mail { get; set; }
        public string? userType { get; set; }
        public string? companyName { get; set; }
        public string? creationType { get; set; }
        public bool IsSelected { get; set; }
    }
    public class UsersResponse
    {
        public List<UserViewModel>? value { get; set; }
    }
}
