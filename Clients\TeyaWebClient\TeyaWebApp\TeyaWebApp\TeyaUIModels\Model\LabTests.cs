﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class LabTests
    {
        public Guid PatientId { get; set; }
        public Guid LabTestsId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PcpId { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? LabTest1 { get; set; }
        public string? LabTest2 { get; set; }
        public string? TestOrganization { get; set; }
        public bool? IsActive { get; set; }
        public string? AssessmentData { get; set; }
        public Guid AssessmentId { get; set; }
    }
}
