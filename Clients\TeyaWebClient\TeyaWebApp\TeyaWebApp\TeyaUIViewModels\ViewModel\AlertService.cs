using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class AlertService : IAlertService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _AlertsServiceURL;
        private readonly ITokenService _tokenService;

        public AlertService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _AlertsServiceURL = Environment.GetEnvironmentVariable("AlertsServiceURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all Alerts (Active & InActive)
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>List of Alerts</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Alert>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Alert>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Get active Alerts
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>List of active Alerts</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Alert>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts/{id}/active";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Alert>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Add a list of Alerts
        /// </summary>
        /// <param name="alerts">List of Alerts to add</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task AddAlertsAsync(List<Alert> alerts, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts";

            // Set IsActive to true for all new alerts
            foreach (var alert in alerts)
            {
                alert.IsActive = true;
            }

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(alerts);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Update an Alert
        /// </summary>
        /// <param name="alert">Alert to update</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateAlertAsync(Alert alert, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts/{alert.AlertId}";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(alert);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Update a list of Alerts
        /// </summary>
        /// <param name="alerts">List of Alerts to update</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateAlertsListAsync(List<Alert> alerts, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(alerts);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Delete an Alert
        /// </summary>
        /// <param name="alert">Alert to delete</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task DeleteAlertByEntityAsync(Alert alert, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts/{alert.AlertId}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Get active Alerts for patients under a specific PCP
        /// </summary>
        /// <param name="pcpId">PCP ID</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>List of active Alerts for all patients under the PCP</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<Alert>> GetActiveAlertsByOrganizationIdAsync(Guid organizationId, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/Alerts/Organization/{organizationId}/active";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Alert>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }
    }
}
