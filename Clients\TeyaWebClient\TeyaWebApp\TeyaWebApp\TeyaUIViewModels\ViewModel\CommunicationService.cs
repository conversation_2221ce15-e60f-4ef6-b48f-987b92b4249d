﻿using Azure;
using Azure.Communication.Email;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TeyaUIViewModels.ViewModel
{
    public class CommunicationService : ICommunicationService
    {
        
        private readonly EmailClient _emailClient;
        private readonly IStringLocalizer<TeyaUIViewModelResource.TeyaUIViewModelsResource> _localizer;
        private const int EmptyRecipientAddressCount = 0;

        public CommunicationService(IStringLocalizer<TeyaUIViewModelResource.TeyaUIViewModelsResource> localizer)
        {
            _localizer = localizer;

            string connectionString = Environment.GetEnvironmentVariable("EMAIL-SERVICE-CONNECTION-STRING");

            if (string.IsNullOrWhiteSpace(connectionString))
                throw new InvalidOperationException(_localizer["EmailConnectionStringNotSet"]);

            _emailClient = new EmailClient(connectionString);
        }

        public async Task<EmailSendOperation> MailService(
            string senderAddress,
            string subject,
            string plainTextContent,
            List<string> recipientAddresses)
        {
            if (string.IsNullOrWhiteSpace(senderAddress))
                throw new ArgumentException(_localizer["SenderAddressEmpty"], nameof(senderAddress));

            if (recipientAddresses == null || recipientAddresses.Count == EmptyRecipientAddressCount)
                throw new ArgumentException(_localizer["RecipientAddressesEmpty"], nameof(recipientAddresses));

            if (string.IsNullOrWhiteSpace(subject))
                throw new ArgumentException(_localizer["SubjectEmpty"], nameof(subject));

            if (string.IsNullOrWhiteSpace(plainTextContent))
                throw new ArgumentException(_localizer["PlainTextContentEmpty"], nameof(plainTextContent));

            var emailRecipients = new EmailRecipients();
            foreach (var recipient in recipientAddresses)
            {
                emailRecipients.To.Add(new Azure.Communication.Email.EmailAddress(recipient));
            }

            var emailMessage = new EmailMessage(
                senderAddress: senderAddress,
                content: new EmailContent(subject)
                {
                    PlainText = plainTextContent
                },
                recipients: emailRecipients);

            try
            {
                return await _emailClient.SendAsync(WaitUntil.Completed, emailMessage);
            }
            catch (RequestFailedException ex)
            {
                throw new Exception(_localizer["FailedToSendEmail", ex.Message], ex);
            }
        }

        public async Task<EmailSendOperation> SendHtmlEmailService(
            string senderAddress,
            string subject,
            string htmlContent,
            List<string> recipientAddresses)
        {
            if (string.IsNullOrWhiteSpace(senderAddress))
                throw new ArgumentException(_localizer["SenderAddressEmpty"], nameof(senderAddress));

            if (recipientAddresses == null || recipientAddresses.Count == 0)
                throw new ArgumentException(_localizer["RecipientAddressesEmpty"], nameof(recipientAddresses));

            if (string.IsNullOrWhiteSpace(subject))
                throw new ArgumentException(_localizer["SubjectEmpty"], nameof(subject));

            if (string.IsNullOrWhiteSpace(htmlContent))
                throw new ArgumentException(_localizer["HtmlContentEmpty"], nameof(htmlContent));

            var emailRecipients = new EmailRecipients();
            foreach (var recipient in recipientAddresses)
            {
                emailRecipients.To.Add(new Azure.Communication.Email.EmailAddress(recipient));
            }

            var emailMessage = new EmailMessage(
                senderAddress: senderAddress,
                content: new EmailContent(subject)
                {
                    Html = htmlContent
                },
                recipients: emailRecipients);

            try
            {
                return await _emailClient.SendAsync(WaitUntil.Completed, emailMessage);
            }
            catch (RequestFailedException ex)
            {
                throw new Exception(_localizer["FailedToSendEmail", ex.Message], ex);
            }
        }
    }
}
