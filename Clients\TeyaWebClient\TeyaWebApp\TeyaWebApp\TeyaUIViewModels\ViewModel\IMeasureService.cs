﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IMeasureService
    {
        Task AddMeasuresAsync(List<Measure> measures);
        Task UpdateMeasuresListAsync(List<Measure> measures);
        Task<string> AskGptAsync(string systemMessage, string userPrompt);
        Task<List<string>> GenerateAlertsAsync(string patientInfo);
        Task<List<Measure>> GetAllMeasuresAsync();

    }
}
