﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using static System.Net.WebRequestMethods;

namespace TeyaUIViewModels.ViewModel
{
    public class TherapeuticInterventionsService : ITherapeuticInterventionsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public TherapeuticInterventionsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<TherapeuticInterventionsData>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TherapeuticInterventionsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task<List<TherapeuticInterventionsData>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{id}/IsActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<TherapeuticInterventionsData>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        public async Task AddTherapeuticInterventionsAsync(List<TherapeuticInterventionsData> medicalHistories, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/AddTherapeuticInterventions/{OrgID}/{Subscription}";

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        public async Task UpdateTherapeuticInterventionsAsync(TherapeuticInterventionsData _TherapeuticInterventions, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/{_TherapeuticInterventions.PatientId}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_TherapeuticInterventions);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdateTherapeuticInterventionsListAsync(List<TherapeuticInterventionsData> medicalHistories, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/UpdateTherapeuticInterventionsList/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicalHistories);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }
        public async Task DeleteTherapeuticInterventionsByEntityAsync(TherapeuticInterventionsData _TherapeuticInterventions, Guid? OrgID, bool Subscription)
        {
            if (_TherapeuticInterventions == null)
            {
                throw new ArgumentNullException(nameof(_TherapeuticInterventions), _localizer["InvalidRecord"]);
            }

            var apiUrl = $"{_EncounterNotes}/api/TherapeuticInterventions/DeleteTherapeuticInterventions/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(_TherapeuticInterventions);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DeleteLogError"]);
            }
        }

    }
}