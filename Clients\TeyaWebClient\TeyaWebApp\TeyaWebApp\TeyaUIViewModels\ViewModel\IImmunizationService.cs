﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IImmunizationService
    {
        Task<List<ImmunizationData>> GetImmunizationsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddImmunizationAsync(List<ImmunizationData> immunization, Guid? OrgID, bool Subscription);
        Task DeleteImmunizationAsync(ImmunizationData Data, Guid? OrgID, bool Subscription);
        Task UpdateImmunizationAsync(ImmunizationData Immunizations, Guid? OrgID, bool Subscription);
        Task<List<ImmunizationData>> GetImmunizationByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateImmunizationListAsync(List<ImmunizationData> immunizationdata, Guid? OrgID, bool Subscription);
    }
}
