﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class FDB_ICD : IModel
    {
        public string ICD_CD { get; set; }
        public string ICD_CD_TYPE { get; set; }
        public string ICD_DESC { get; set; }
        public string ICD_DESC_SOURCE_CD { get; set; }
        public string ICD_STATUS_CD { get; set; }
        public string ICD_FIRST_DT { get; set; }
        public string? ICD_LAST_DT { get; set; } // Nullable
        public string ICD_BILLABLE_IND { get; set; }
    }
}
