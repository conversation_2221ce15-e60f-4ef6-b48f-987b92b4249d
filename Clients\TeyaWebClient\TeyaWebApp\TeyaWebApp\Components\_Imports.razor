﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using TeyaWebApp
@using TeyaWebApp.Components
@using MudBlazor
@using Microsoft.Extensions.Localization
@using MudBlazor.Services
@using TeyaWebApp.TeyaAIScribeResource
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor
@using TeyaUIViewModels.ViewModel
@using TeyaUIModels.Model
@using TeyaWebApp.Components.GenericElements