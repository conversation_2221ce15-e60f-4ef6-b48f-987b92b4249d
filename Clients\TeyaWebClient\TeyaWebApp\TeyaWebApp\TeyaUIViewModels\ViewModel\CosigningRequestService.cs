using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class CosigningRequestService : ICosigningRequestService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly ITokenService _tokenService;
        private readonly ICosigningStateService _stateService;
        private readonly string _encounterNotesUrl;

        public CosigningRequestService(
            HttpClient httpClient,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer,
            ITokenService tokenService,
            ICosigningStateService stateService)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _localizer = localizer;
            _tokenService = tokenService;
            _stateService = stateService;
            _encounterNotesUrl = Environment.GetEnvironmentVariable("EncounterNotesURL") ?? _configuration["EncounterNotesApiUrl"];
        }

        public async Task<Guid> CreateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription)
        {
            try
            {
                if (request == null)
                {
                    throw new ArgumentNullException(nameof(request), "Request cannot be null");
                }

                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/create/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(result);
                    return jsonDoc.RootElement.GetProperty("RequestId").GetGuid();
                }

                throw new HttpRequestException($"Failed to create cosigning request. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error creating cosigning request: {ex.Message}", ex);
            }
        }

        public async Task UpdateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription)
        {
            try
            {
                if (request == null)
                {
                    throw new ArgumentNullException(nameof(request), "Request cannot be null");
                }

                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/update/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Failed to update cosigning request. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error updating cosigning request: {ex.Message}", ex);
            }
        }

        public async Task CancelRequestAsync(Guid requestId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/cancel/{requestId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Failed to cancel cosigning request. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error cancelling cosigning request: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetByRequesterIdAsync(Guid requesterId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/by-requester/{requesterId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<CosigningRequest>>() ?? Enumerable.Empty<CosigningRequest>();
                }

                throw new HttpRequestException($"Failed to get requests by requester {requesterId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error retrieving requests by requester {requesterId}: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetByReviewerIdAsync(Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/by-reviewer/{reviewerId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<CosigningRequest>>() ?? Enumerable.Empty<CosigningRequest>();
                }

                throw new HttpRequestException($"Failed to get requests by reviewer {reviewerId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error retrieving requests by reviewer {reviewerId}: {ex.Message}", ex);
            }
        }

        public async Task<CosigningRequest> GetByIdAsync(Guid requestId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/{requestId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<CosigningRequest>();
                }

                return null; // Request not found
            }
            catch (Exception)
            {
                return null; // Return null if there's an error
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetByRecordIdAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/by-record/{recordId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<CosigningRequest>>() ?? Enumerable.Empty<CosigningRequest>();
                }

                throw new HttpRequestException($"Failed to get requests for record {recordId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error retrieving requests for record {recordId}: {ex.Message}", ex);
            }
        }

        public async Task ApproveRequestAsync(Guid requestId, Guid reviewerId, string reviewerName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/approve/{requestId}/{reviewerId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(reviewerName), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to approve request {requestId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }

                // Notify state service of approval
                await _stateService.NotifyRequestApproved(requestId);
            }
            catch (Exception ex) when (!(ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error approving request {requestId}: {ex.Message}", ex);
            }
        }

        public async Task RequestChangesAsync(Guid requestId, Guid reviewerId, string reviewerName, string comment, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/request-changes/{requestId}/{reviewerId}/{organizationId}/{subscription}";
                var requestData = new { ReviewerName = reviewerName, Comment = comment };
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(requestData), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to request changes for request {requestId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }

                // Notify state service of changes requested
                await _stateService.NotifyRequestChangesRequested(requestId);
            }
            catch (Exception ex) when (!(ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error requesting changes for request {requestId}: {ex.Message}", ex);
            }
        }

        public async Task AddCommentAsync(Guid requestId, Guid commenterId, string commenterName, string comment, Guid organizationId, bool subscription)
        {
            try
            {
                if (string.IsNullOrEmpty(comment))
                {
                    throw new ArgumentException("Comment cannot be null or empty", nameof(comment));
                }

                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/comment/{requestId}/{commenterId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(comment), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to add comment. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error adding comment: {ex.Message}", ex);
            }
        }

        public async Task ResolveCommentAsync(Guid requestId, Guid commentId, Guid resolvedById, string resolvedByName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/resolve-comment/{requestId}/{commentId}/{resolvedById}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(resolvedByName), Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to resolve comment {commentId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }

                // Notify state service of comment resolution
                await _stateService.NotifyCommentResolved(requestId, commentId);
            }
            catch (Exception ex) when (!(ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error resolving comment {commentId}: {ex.Message}", ex);
            }
        }

        public async Task<bool> HasPendingRequestAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/has-pending/{recordId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<bool>();
                }

                return false; // Return false if there's an error
            }
            catch (Exception)
            {
                return false; // Return false if there's an error
            }
        }

        public async Task<int> GetPendingRequestCountAsync(Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/CosigningRequest/pending-count/{reviewerId}/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<int>();
                }

                return 0; // Return 0 if there's an error getting the count
            }
            catch (Exception)
            {
                return 0; // Return 0 if there's an error getting the count
            }
        }

        public async Task<CosigningRequest> GetActiveRequestAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var requests = await GetByRecordIdAsync(recordId, organizationId, subscription);
                return requests.FirstOrDefault(r => r.Status == CosigningRequestStatus.Pending);
            }
            catch (Exception)
            {
                return null; // Return null if there's an error
            }
        }
    }
}
