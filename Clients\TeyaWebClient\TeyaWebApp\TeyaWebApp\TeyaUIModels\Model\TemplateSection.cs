﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class TemplateSection : IModel
    {
        public string SectionName { get; set; }
        public List<TemplateFieldModel> Fields { get; set; } = new List<TemplateFieldModel>();
        public bool IsExpanded { get; set; } = false; // Add this property

    }
}