﻿using TeyaUIModels.Model;

namespace TeyaWebApp.ViewModel
{
    public interface IProductService
    {
        Task<List<Product>> GetProductsAsync();
        Task<Product> GetProductByIdAsync(Guid id, Guid orgId, bool subscription);
        Task UpdateProductAsync(Guid id, Product product);
        Task DeleteProductByIdAsync(Guid id, Guid orgId, bool subscription);
        Task DeleteProductByEntityAsync(Product product);
        Task RegisterProductsAsync(List<ProductRegistrationDto> registrations);
        Task<List<Member>> GetMembersForProductAsync(Guid productId, Guid orgId, bool subscription);
        Task UpdateMembersAccessAsync(Guid productId, MemberAccessUpdate memberAccessUpdates, Guid orgId, bool subscription);
    }
}
