﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System.Net.Http.Json;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using TeyaWebApp.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class ProductService : IProductService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _MemberService;
        private const bool Subscription = false;

        public ProductService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<List<Product>> GetProductsAsync()
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Product>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["ProductRetrievalFailure"]);
            }
        }

        public async Task UpdateMembersAccessAsync(Guid productId, MemberAccessUpdate memberAccessUpdates, Guid OrgID, bool Subscription)
        {
            try
            {
                var productIdParameter = "productId";
                var requestUrl = $"{_MemberService}/api/Products/updateAccess/{OrgID}/{Subscription}?{productIdParameter}={productId}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = JsonSerializer.Serialize(memberAccessUpdates);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, requestUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateAccessFailure"], ex);
            }
        }

        public async Task<Product> GetProductByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/{id}/{orgId}/{subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<Product>();
            }
            else
            {
                throw new HttpRequestException(_localizer["ProductRetrievalFailure"]);
            }
        }

        public async Task UpdateProductAsync(Guid id, Product product)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/{id}";
            var content = new StringContent(JsonSerializer.Serialize(product), Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task DeleteProductByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/{id}/{orgId}/{subscription}";

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task DeleteProductByEntityAsync(Product product)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/(entity)";
            var content = new StringContent(JsonSerializer.Serialize(product), Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task RegisterProductsAsync(List<ProductRegistrationDto> registrations)
        {
            if (registrations == null || registrations.Count == 0)
            {
                throw new ArgumentException(_localizer["NoProduct"]);
            }

            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/registration";
            var content = new StringContent(JsonSerializer.Serialize(registrations), Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task<List<Member>> GetMembersForProductAsync(Guid productId, Guid orgId, bool subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var requestUrl = $"{_MemberService}/api/Products/{productId}/members/{orgId}/{subscription}";

            var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Member>>();
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return new List<Member>();
            }
            else
            {
                throw new HttpRequestException(_localizer["MemberRetrievalFailure"]);
            }
        }
    }
}