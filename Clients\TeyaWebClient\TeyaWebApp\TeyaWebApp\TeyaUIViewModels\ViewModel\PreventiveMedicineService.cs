﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PreventiveMedicineService : IPreventiveMedicineService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public PreventiveMedicineService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        private void AddAuthHeader(HttpRequestMessage requestMessage)
        {
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        }

        // Preventive Medicines Grid
        public async Task<List<PreventiveMedicines>> GetPreventiveMedicinesByPatientIdAndIsActiveAsync(Guid id,Guid OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PreventiveMedicine/GetActiveByPatient/{id}/{OrgID}/{Subscription}";

            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PreventiveMedicines>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task AddPreventiveMedicineAsync(List<PreventiveMedicines> medicines, Guid OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PreventiveMedicine/Add/{OrgID}/{Subscription}";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(medicines), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdatePreventiveMedicinesInBulk(List<PreventiveMedicines> medicines, Guid OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PreventiveMedicine/Update/{OrgID}/{Subscription}";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(medicines), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        // Setup - PM Category
        public async Task<List<PMCategory>> GetAllPMCategoriesAsync()
        {
            var apiUrl = $"{_EncounterNotes}/api/PMCategory";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PMCategory>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task AddPMCategoryAsync(List<PMCategory> category)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMCategory/add";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(category), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdatePMCategoryAsync(List<PMCategory> category)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMCategory/Update";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(category), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        // Setup - PM SubCategory

        public async Task<List<PMSubCategory>> GetAllPMSubCategoriesAsync()
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSubCategory";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PMSubCategory>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }
        public async Task<List<PMSubCategory>> GetPMSubCategoriesByPMCategoryIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSubCategory/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PMSubCategory>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task AddPMSubCategoryAsync(List<PMSubCategory> category)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSubCategory";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(category), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdatePMSubCategoryAsync(List<PMSubCategory> category)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSubCategory/Update";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(category), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        // Setup - Symptoms

        public async Task<List<PMSymptoms>> GetAllPMSymptomsAsync()
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSymptoms";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PMSymptoms>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }
        public async Task<List<PMSymptoms>> GetPMSymptomsBySubCategoryIdAsync(Guid id)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSymptoms/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<PMSymptoms>>();
            }
            throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
        }

        public async Task AddPMSymptomsAsync(List<PMSymptoms> symptoms)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSymptoms";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(symptoms), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task UpdatePMSymptoms(List<PMSymptoms> symptoms)
        {
            var apiUrl = $"{_EncounterNotes}/api/PMSymptoms/Update";
            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(symptoms), Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl) { Content = content };
            AddAuthHeader(requestMessage);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
    }
}
