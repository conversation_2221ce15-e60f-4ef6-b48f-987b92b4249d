﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IPreDefinedPageRoleMappingService
    {
        Task<IEnumerable<string>> GetRolesByPagePathAsync(string pagePath);
        Task<IEnumerable<PreDefinedPageRoleMappingData>> GetPagesByRoleNameAsync(string roleName);
        Task<IEnumerable<PreDefinedPageRoleMappingData>> GetPreDefinedPageRoleMappingsAsync();
        Task<PreDefinedPageRoleMappingData> GetPreDefinedPageRoleMappingByIdAsync(Guid id);
        Task AddPreDefinedPageRoleMappingAsync(PreDefinedPageRoleMappingData PreDefinedPageRoleMapping);
        Task UpdatePreDefinedPageRoleMappingAsync(PreDefinedPageRoleMappingData PreDefinedPageRoleMapping);
        Task DeletePreDefinedPageRoleMappingByIdAsync(Guid id);
    }
}
