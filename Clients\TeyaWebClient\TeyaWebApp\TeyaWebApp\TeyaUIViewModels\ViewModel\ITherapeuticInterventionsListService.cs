﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ITherapeuticInterventionsListService
    {
        Task<List<TherapeuticInterventionsListCode>> GetAllTherapeuticInterventionsListCodesAsync();
        Task<List<TherapeuticInterventionsListCode>> GetTherapeuticInterventionsBySearchTerm(string SearchTerm);
    }
}
