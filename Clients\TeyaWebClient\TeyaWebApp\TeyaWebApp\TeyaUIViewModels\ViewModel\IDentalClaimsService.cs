﻿using System.Net.Http;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IDentalClaimsService
    {
        Task<List<CompleteDentalClaims>> GetDentalClaimsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CompleteDentalClaims>> GetAllDentalClaimsAsync(Guid? OrgID, bool Subscription);
        Task AddDentalClaimsAsync(List<CompleteDentalClaims> completeDentalClaims, Guid? OrgID, bool Subscription);
        Task UpdateDentalClaimsAsync(CompleteDentalClaims DentalClaims, Guid? OrgID, bool Subscription);
        Task DeleteDentalClaimsAsync(CompleteDentalClaims DentalClaims, Guid id, Guid? OrgID, bool Subscription);


    }
}