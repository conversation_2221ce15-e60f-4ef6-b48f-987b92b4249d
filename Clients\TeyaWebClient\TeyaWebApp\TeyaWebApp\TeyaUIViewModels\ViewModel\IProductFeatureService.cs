﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IProductFeatureService
    {
        Task<IEnumerable<ProductFeature>> GetAllProductFeaturesAsync();
        Task<ProductFeature> GetProductFeatureByIdAsync(Guid id);
        Task AddProductFeatureAsync(ProductFeature plan);
        Task UpdateProductFeatureAsync(ProductFeature plan);
        Task DeleteProductFeatureByIdAsync(Guid id);
    }
}
