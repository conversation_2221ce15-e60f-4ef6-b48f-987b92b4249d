﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class TokenService : ITokenService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IStringLocalizer<TokenService> _localizer;
        private readonly NavigationManager _navigationManager;

        public TokenService(
            IHttpContextAccessor httpContextAccessor,
            IStringLocalizer<TokenService> localizer,
            NavigationManager navigationManager)
        {
            _httpContextAccessor = httpContextAccessor;
            _localizer = localizer;
            _navigationManager = navigationManager;
        }
        // ✅ Get/Set AccessToken from Session
        public string? AccessToken
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken", value ?? "");
        }

        public string? AccessToken2
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken2");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken2", value ?? "");
        }

        public string? UserDetails
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("UserDetails");
            set => _httpContextAccessor.HttpContext?.Session.SetString("UserDetails", value ?? "");
        }
        public async Task<string?> GetValidatedAccessTokenAsync()
        {
            var token = AccessToken;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

        public async Task<string?> GetValidatedAccessToken2Async()
        {
            var token = AccessToken2;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }
        public Task<string> GetAccessTokenAsync()
        {
            throw new NotImplementedException();
        }
    }
}
