﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPhysicalTherapyService
    {
        Task<List<PhysicalTherapyData>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<PhysicalTherapyData>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddPhysicalTherapyAsync(List<PhysicalTherapyData> medicalHistories, Guid? OrgID, bool Subscription);
        Task UpdatePhysicalTherapyAsync(PhysicalTherapyData _PhysicalTherapy, Guid? OrgID, bool Subscription);
        Task UpdatePhysicalTherapyListAsync(List<PhysicalTherapyData> medicalHistories, Guid? OrgID, bool Subscription);
        Task DeletePhysicalTherapyByEntityAsync(PhysicalTherapyData _PhysicalTherapy, Guid? OrgID, bool Subscription);
    }
}
