﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class OrderSetLabTests
    {
        public Guid LabTestsId { get; set; }
        public string? LabTest1 { get; set; }
        public string? LabTest2 { get; set; }
        public string? TestOrganization { get; set; }
        public string? AssessmentData { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public Guid? AssessmentId { get; set; }
        public Guid OrderSetId { get; set; }

    }
}
