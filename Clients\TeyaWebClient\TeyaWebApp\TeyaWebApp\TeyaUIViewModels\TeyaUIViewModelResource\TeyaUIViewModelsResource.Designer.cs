﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TeyaUIViewModels.TeyaUIViewModelResource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class TeyaUIViewModelsResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal TeyaUIViewModelsResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("TeyaUIViewModels.TeyaUIViewModelResource.TeyaUIViewModelsResource", typeof(TeyaUIViewModelsResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {productId}.
        /// </summary>
        public static string _productId_ {
            get {
                return ResourceManager.GetString("{productId}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ABOUT.
        /// </summary>
        public static string ABOUT {
            get {
                return ResourceManager.GetString("ABOUT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access record not found.
        /// </summary>
        public static string AccessNotFound {
            get {
                return ResourceManager.GetString("AccessNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AccessTokenMissing.
        /// </summary>
        public static string AccessTokenMissing {
            get {
                return ResourceManager.GetString("AccessTokenMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access updated successfully.
        /// </summary>
        public static string AccessUpdateSuccessful {
            get {
                return ResourceManager.GetString("AccessUpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account deleted successfully..
        /// </summary>
        public static string AccountDeleted {
            get {
                return ResourceManager.GetString("AccountDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Member.
        /// </summary>
        public static string AddMember {
            get {
                return ResourceManager.GetString("AddMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AddPreDefinedPageRoleMappingFailed.
        /// </summary>
        public static string AddPreDefinedPageRoleMappingFailed {
            get {
                return ResourceManager.GetString("AddPreDefinedPageRoleMappingFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /ambientsolution.
        /// </summary>
        public static string AmbientSolutionRoute {
            get {
                return ResourceManager.GetString("AmbientSolutionRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  API Response: {ResponseData}.
        /// </summary>
        public static string API_Response___ResponseData_ {
            get {
                return ResourceManager.GetString("API Response: {ResponseData}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:DeleteRegistrationMemberUrl.
        /// </summary>
        public static string ApiSettings_DeleteRegistrationMemberUrl {
            get {
                return ResourceManager.GetString("ApiSettings:DeleteRegistrationMemberUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:MembersUrl.
        /// </summary>
        public static string ApiSettings_MembersUrl {
            get {
                return ResourceManager.GetString("ApiSettings:MembersUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:ProductsUrl.
        /// </summary>
        public static string ApiSettings_ProductsUrl {
            get {
                return ResourceManager.GetString("ApiSettings:ProductsUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:RegistrationUrl.
        /// </summary>
        public static string ApiSettings_RegistrationUrl {
            get {
                return ResourceManager.GetString("ApiSettings:RegistrationUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ApiSettings:UpdateAccessUrl.
        /// </summary>
        public static string ApiSettings_UpdateAccessUrl {
            get {
                return ResourceManager.GetString("ApiSettings:UpdateAccessUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to application/json.
        /// </summary>
        public static string application_json {
            get {
                return ResourceManager.GetString("application/json", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the selected features?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_the_selected_features_ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete the selected features?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the selected product?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_the_selected_product_ {
            get {
                return ResourceManager.GetString("Are you sure you want to delete the selected product?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AudioBaseUrl.
        /// </summary>
        public static string AudioBaseUrl {
            get {
                return ResourceManager.GetString("AudioBaseUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to authentication/logout.
        /// </summary>
        public static string authentication_logout {
            get {
                return ResourceManager.GetString("authentication/logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choices.
        /// </summary>
        public static string Choices {
            get {
                return ResourceManager.GetString("Choices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to city.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to companyName.
        /// </summary>
        public static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Delete.
        /// </summary>
        public static string Confirm_Delete {
            get {
                return ResourceManager.GetString("Confirm Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        public static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONTACT US.
        /// </summary>
        public static string CONTACTUS {
            get {
                return ResourceManager.GetString("CONTACTUS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to country.
        /// </summary>
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CreateUser.
        /// </summary>
        public static string CreateUser {
            get {
                return ResourceManager.GetString("CreateUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CustomMessage.
        /// </summary>
        public static string CustomMessage {
            get {
                return ResourceManager.GetString("CustomMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A database error occurred..
        /// </summary>
        public static string DatabaseError {
            get {
                return ResourceManager.GetString("DatabaseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateOfBirth.
        /// </summary>
        public static string DateOfBirth {
            get {
                return ResourceManager.GetString("DateOfBirth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeleteAccount.
        /// </summary>
        public static string DeleteAccount {
            get {
                return ResourceManager.GetString("DeleteAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deleting account..
        /// </summary>
        public static string DeleteError {
            get {
                return ResourceManager.GetString("DeleteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeleteFailedStatusCode.
        /// </summary>
        public static string DeleteFailedStatusCode {
            get {
                return ResourceManager.GetString("DeleteFailedStatusCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item is not deleted.
        /// </summary>
        public static string DeleteLogError {
            get {
                return ResourceManager.GetString("DeleteLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Successful.
        /// </summary>
        public static string DeleteSuccessful {
            get {
                return ResourceManager.GetString("DeleteSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to department.
        /// </summary>
        public static string Department {
            get {
                return ResourceManager.GetString("Department", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to displayName.
        /// </summary>
        public static string Display_Name {
            get {
                return ResourceManager.GetString("Display_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DisplayName.
        /// </summary>
        public static string DisplayName {
            get {
                return ResourceManager.GetString("DisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Facility Name.
        /// </summary>
        public static string Duplicate_Facility_Name {
            get {
                return ResourceManager.GetString("Duplicate Facility Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Role Name.
        /// </summary>
        public static string Duplicate_Role_Name {
            get {
                return ResourceManager.GetString("Duplicate Role Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EditUser.
        /// </summary>
        public static string EditUser {
            get {
                return ResourceManager.GetString("EditUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EHR INTEGRATIONS.
        /// </summary>
        public static string EHRINTEGRATIONS {
            get {
                return ResourceManager.GetString("EHRINTEGRATIONS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EmailRequired.
        /// </summary>
        public static string EmailRequired {
            get {
                return ResourceManager.GetString("EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer.
        /// </summary>
        public static string Employer {
            get {
                return ResourceManager.GetString("Employer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employer Details.
        /// </summary>
        public static string EmployerDetails {
            get {
                return ResourceManager.GetString("EmployerDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterCustomMessage.
        /// </summary>
        public static string EnterCustomMessage {
            get {
                return ResourceManager.GetString("EnterCustomMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterDisplayName.
        /// </summary>
        public static string EnterDisplayName {
            get {
                return ResourceManager.GetString("EnterDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterEmailAddress.
        /// </summary>
        public static string EnterEmailAddress {
            get {
                return ResourceManager.GetString("EnterEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EnterRedirectUrl.
        /// </summary>
        public static string EnterRedirectUrl {
            get {
                return ResourceManager.GetString("EnterRedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while processing..
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error deserializing response: {0}.
        /// </summary>
        public static string Error_deserializing_response___0_ {
            get {
                return ResourceManager.GetString("Error deserializing response: {0}", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error fetching full user details.
        /// </summary>
        public static string Error_fetching_full_user_details {
            get {
                return ResourceManager.GetString("Error fetching full user details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Getting Features for selected Product..
        /// </summary>
        public static string Error_Getting_Features_for_selected_Product_ {
            get {
                return ResourceManager.GetString("Error Getting Features for selected Product.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while deleting selected features..
        /// </summary>
        public static string Error_occurred_while_deleting_selected_features_ {
            get {
                return ResourceManager.GetString("Error occurred while deleting selected features.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while deleting selected products..
        /// </summary>
        public static string Error_occurred_while_deleting_selected_products_ {
            get {
                return ResourceManager.GetString("Error occurred while deleting selected products.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while showing features for selected products..
        /// </summary>
        public static string Error_occurred_while_showing_features_for_selected_products_ {
            get {
                return ResourceManager.GetString("Error occurred while showing features for selected products.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error parsing user details.
        /// </summary>
        public static string Error_parsing_user_details {
            get {
                return ResourceManager.GetString("Error parsing user details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error registering member.
        /// </summary>
        public static string Error_registering_member {
            get {
                return ResourceManager.GetString("Error registering member", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error submitting product data..
        /// </summary>
        public static string Error_submitting_product_data_ {
            get {
                return ResourceManager.GetString("Error submitting product data.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorAddingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorAddingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorAddingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorCreatingUser.
        /// </summary>
        public static string ErrorCreatingUser {
            get {
                return ResourceManager.GetString("ErrorCreatingUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingMember.
        /// </summary>
        public static string ErrorDeletingMember {
            get {
                return ResourceManager.GetString("ErrorDeletingMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorDeletingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorDeletingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDeletingUsers.
        /// </summary>
        public static string ErrorDeletingUsers {
            get {
                return ResourceManager.GetString("ErrorDeletingUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorDuringMemberSubmissionUpdate.
        /// </summary>
        public static string ErrorDuringMemberSubmissionUpdate {
            get {
                return ResourceManager.GetString("ErrorDuringMemberSubmissionUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error During Registration.
        /// </summary>
        public static string ErrorDuringRegistration {
            get {
                return ResourceManager.GetString("ErrorDuringRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingAllPreDefinedPageRoleMappings.
        /// </summary>
        public static string ErrorFetchingAllPreDefinedPageRoleMappings {
            get {
                return ResourceManager.GetString("ErrorFetchingAllPreDefinedPageRoleMappings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Processing Complaints.
        /// </summary>
        public static string ErrorFetchingComplaints {
            get {
                return ResourceManager.GetString("ErrorFetchingComplaints", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingFullUserDetails.
        /// </summary>
        public static string ErrorFetchingFullUserDetails {
            get {
                return ResourceManager.GetString("ErrorFetchingFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Licenses.
        /// </summary>
        public static string ErrorFetchingLicenses {
            get {
                return ResourceManager.GetString("ErrorFetchingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingMemberById.
        /// </summary>
        public static string ErrorFetchingMemberById {
            get {
                return ResourceManager.GetString("ErrorFetchingMemberById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Fetching Members For Product.
        /// </summary>
        public static string ErrorFetchingMembersForProduct {
            get {
                return ResourceManager.GetString("ErrorFetchingMembersForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingPreDefinedPageRoleMappingById.
        /// </summary>
        public static string ErrorFetchingPreDefinedPageRoleMappingById {
            get {
                return ResourceManager.GetString("ErrorFetchingPreDefinedPageRoleMappingById", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error fetching products.
        /// </summary>
        public static string ErrorFetchingProducts {
            get {
                return ResourceManager.GetString("ErrorFetchingProducts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorFetchingUsers.
        /// </summary>
        public static string ErrorFetchingUsers {
            get {
                return ResourceManager.GetString("ErrorFetchingUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorGettingFullUserDetails.
        /// </summary>
        public static string ErrorGettingFullUserDetails {
            get {
                return ResourceManager.GetString("ErrorGettingFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorGettingUserDetails.
        /// </summary>
        public static string ErrorGettingUserDetails {
            get {
                return ResourceManager.GetString("ErrorGettingUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorInvitingUser.
        /// </summary>
        public static string ErrorInvitingUser {
            get {
                return ResourceManager.GetString("ErrorInvitingUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorParsingUserDetails.
        /// </summary>
        public static string ErrorParsingUserDetails {
            get {
                return ResourceManager.GetString("ErrorParsingUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorRegisteringMember.
        /// </summary>
        public static string ErrorRegisteringMember {
            get {
                return ResourceManager.GetString("ErrorRegisteringMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Licenses.
        /// </summary>
        public static string ErrorSavingLicenses {
            get {
                return ResourceManager.GetString("ErrorSavingLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Saving Member Access Updates.
        /// </summary>
        public static string ErrorSavingMemberAccessUpdates {
            get {
                return ResourceManager.GetString("ErrorSavingMemberAccessUpdates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingFacilityData.
        /// </summary>
        public static string ErrorSubmittingFacilityData {
            get {
                return ResourceManager.GetString("ErrorSubmittingFacilityData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorSubmittingMemberData.
        /// </summary>
        public static string ErrorSubmittingMemberData {
            get {
                return ResourceManager.GetString("ErrorSubmittingMemberData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ErrorUpdatingPreDefinedPageRoleMapping.
        /// </summary>
        public static string ErrorUpdatingPreDefinedPageRoleMapping {
            get {
                return ResourceManager.GetString("ErrorUpdatingPreDefinedPageRoleMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExceptionOccurred.
        /// </summary>
        public static string ExceptionOccurred {
            get {
                return ResourceManager.GetString("ExceptionOccurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ExtensionCompanyName.
        /// </summary>
        public static string ExtensionCompanyName {
            get {
                return ResourceManager.GetString("ExtensionCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FacilityRegistered.
        /// </summary>
        public static string FacilityRegistered {
            get {
                return ResourceManager.GetString("FacilityRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save user data..
        /// </summary>
        public static string Failed {
            get {
                return ResourceManager.GetString("Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToGetFullUserDetails.
        /// </summary>
        public static string FailedToGetFullUserDetails {
            get {
                return ResourceManager.GetString("FailedToGetFullUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToGetUserDetails.
        /// </summary>
        public static string FailedToGetUserDetails {
            get {
                return ResourceManager.GetString("FailedToGetUserDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToSendInvitation.
        /// </summary>
        public static string FailedToSendInvitation {
            get {
                return ResourceManager.GetString("FailedToSendInvitation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FailedToUpdateUser.
        /// </summary>
        public static string FailedToUpdateUser {
            get {
                return ResourceManager.GetString("FailedToUpdateUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feature grid is not initialized..
        /// </summary>
        public static string Feature_grid_is_not_initialized_ {
            get {
                return ResourceManager.GetString("Feature grid is not initialized.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Features.
        /// </summary>
        public static string Features {
            get {
                return ResourceManager.GetString("Features", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FirstName.
        /// </summary>
        public static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GetByIdFailed.
        /// </summary>
        public static string GetByIdFailed {
            get {
                return ResourceManager.GetString("GetByIdFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while fetching members..
        /// </summary>
        public static string GetLogError {
            get {
                return ResourceManager.GetString("GetLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GivenName.
        /// </summary>
        public static string Given_Name {
            get {
                return ResourceManager.GetString("Given Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to givenName.
        /// </summary>
        public static string GivenName {
            get {
                return ResourceManager.GetString("GivenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guardian.
        /// </summary>
        public static string Guardian {
            get {
                return ResourceManager.GetString("Guardian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GuardianDetails.
        /// </summary>
        public static string GuardianDetails {
            get {
                return ResourceManager.GetString("GuardianDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Guest.
        /// </summary>
        public static string Guest {
            get {
                return ResourceManager.GetString("Guest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to U.
        /// </summary>
        public static string GusestUser {
            get {
                return ResourceManager.GetString("GusestUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTTP Request Error:.
        /// </summary>
        public static string HTTPRequestError {
            get {
                return ResourceManager.GetString("HTTPRequestError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to id.
        /// </summary>
        public static string Id {
            get {
                return ResourceManager.GetString("Id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance.
        /// </summary>
        public static string Insurance {
            get {
                return ResourceManager.GetString("Insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InsuranceDetails.
        /// </summary>
        public static string InsuranceDetails {
            get {
                return ResourceManager.GetString("InsuranceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvalidEmailFormat.
        /// </summary>
        public static string InvalidEmailFormat {
            get {
                return ResourceManager.GetString("InvalidEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The given Id is not valid.
        /// </summary>
        public static string InvalidId {
            get {
                return ResourceManager.GetString("InvalidId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not a valid member.
        /// </summary>
        public static string InvalidMember {
            get {
                return ResourceManager.GetString("InvalidMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteRedirectUrl {
            get {
                return ResourceManager.GetString("InviteRedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteRedirectUrlDev {
            get {
                return ResourceManager.GetString("InviteRedirectUrlDev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to https://teyawebappdev.mangocoast-349cc47a.eastus.azurecontainerapps.io.
        /// </summary>
        public static string InviteUrl {
            get {
                return ResourceManager.GetString("InviteUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InviteUser.
        /// </summary>
        public static string InviteUser {
            get {
                return ResourceManager.GetString("InviteUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /inviteuser.
        /// </summary>
        public static string InviteUserPath {
            get {
                return ResourceManager.GetString("InviteUserPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to jobTitle.
        /// </summary>
        public static string JobTitle {
            get {
                return ResourceManager.GetString("JobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while deserializing the response: {0}.
        /// </summary>
        public static string JsonDeserializationError {
            get {
                return ResourceManager.GetString("JsonDeserializationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LastName.
        /// </summary>
        public static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License Retrieval Failure.
        /// </summary>
        public static string LicenseRetrievalFailure {
            get {
                return ResourceManager.GetString("LicenseRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LoadingUserData.
        /// </summary>
        public static string LoadingUserData {
            get {
                return ResourceManager.GetString("LoadingUserData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOGIN.
        /// </summary>
        public static string LOGIN {
            get {
                return ResourceManager.GetString("LOGIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mail.
        /// </summary>
        public static string Mail {
            get {
                return ResourceManager.GetString("Mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MailNickname.
        /// </summary>
        public static string MailNickname {
            get {
                return ResourceManager.GetString("MailNickname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to city.
        /// </summary>
        public static string ManageCity {
            get {
                return ResourceManager.GetString("ManageCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to companyName.
        /// </summary>
        public static string ManageCompanyName {
            get {
                return ResourceManager.GetString("ManageCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to country.
        /// </summary>
        public static string ManageCountry {
            get {
                return ResourceManager.GetString("ManageCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to department.
        /// </summary>
        public static string ManageDepartment {
            get {
                return ResourceManager.GetString("ManageDepartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to displayName.
        /// </summary>
        public static string ManageDisplayName {
            get {
                return ResourceManager.GetString("ManageDisplayName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to extensionCompanyName.
        /// </summary>
        public static string ManageExtensionCompanyName {
            get {
                return ResourceManager.GetString("ManageExtensionCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to givenName.
        /// </summary>
        public static string ManageGivenName {
            get {
                return ResourceManager.GetString("ManageGivenName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to id.
        /// </summary>
        public static string ManageId {
            get {
                return ResourceManager.GetString("ManageId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to jobTitle.
        /// </summary>
        public static string ManageJobTitle {
            get {
                return ResourceManager.GetString("ManageJobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mail.
        /// </summary>
        public static string ManageMail {
            get {
                return ResourceManager.GetString("ManageMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mobilePhone.
        /// </summary>
        public static string ManageMobilePhone {
            get {
                return ResourceManager.GetString("ManageMobilePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to officeLocation.
        /// </summary>
        public static string ManageOfficeLocation {
            get {
                return ResourceManager.GetString("ManageOfficeLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to postalCode.
        /// </summary>
        public static string ManagePostalCode {
            get {
                return ResourceManager.GetString("ManagePostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /manageprofile.
        /// </summary>
        public static string ManageProfile {
            get {
                return ResourceManager.GetString("ManageProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to state.
        /// </summary>
        public static string ManageState {
            get {
                return ResourceManager.GetString("ManageState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to streetAddress.
        /// </summary>
        public static string ManageStreetAddress {
            get {
                return ResourceManager.GetString("ManageStreetAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to surname.
        /// </summary>
        public static string ManageSurname {
            get {
                return ResourceManager.GetString("ManageSurname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to userType.
        /// </summary>
        public static string ManageUserType {
            get {
                return ResourceManager.GetString("ManageUserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manag Your Teya Account.
        /// </summary>
        public static string ManagYourTeyaAccount {
            get {
                return ResourceManager.GetString("ManagYourTeyaAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member registered successfully!.
        /// </summary>
        public static string Member_registered_successfully_ {
            get {
                return ResourceManager.GetString("Member registered successfully!", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberDeletedSuccessfully.
        /// </summary>
        public static string MemberDeletedSuccessfully {
            get {
                return ResourceManager.GetString("MemberDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberDeletionFailed.
        /// </summary>
        public static string MemberDeletionFailed {
            get {
                return ResourceManager.GetString("MemberDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Member Not Found.
        /// </summary>
        public static string MemberNotFound {
            get {
                return ResourceManager.GetString("MemberNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberRegistered.
        /// </summary>
        public static string MemberRegistered {
            get {
                return ResourceManager.GetString("MemberRegistered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberRegisteredSuccessfully.
        /// </summary>
        public static string MemberRegisteredSuccessfully {
            get {
                return ResourceManager.GetString("MemberRegisteredSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred during member registration..
        /// </summary>
        public static string MemberRegistrationError {
            get {
                return ResourceManager.GetString("MemberRegistrationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to http://localhost/MemberServiceApi/api/Products.
        /// </summary>
        public static string MemberServiceApi_base_url {
            get {
                return ResourceManager.GetString("MemberServiceApi_base_url", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MemberUpdated.
        /// </summary>
        public static string MemberUpdated {
            get {
                return ResourceManager.GetString("MemberUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mic.
        /// </summary>
        public static string Mic {
            get {
                return ResourceManager.GetString("Mic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Misc.
        /// </summary>
        public static string Misc {
            get {
                return ResourceManager.GetString("Misc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MiscellaneousDetails.
        /// </summary>
        public static string MiscellaneousDetails {
            get {
                return ResourceManager.GetString("MiscellaneousDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MobilePhone.
        /// </summary>
        public static string MobilePhone {
            get {
                return ResourceManager.GetString("MobilePhone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to name.
        /// </summary>
        public static string NameClaim {
            get {
                return ResourceManager.GetString("NameClaim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No features selected for deletion..
        /// </summary>
        public static string No_features_selected_for_deletion_ {
            get {
                return ResourceManager.GetString("No features selected for deletion.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No products selected for deletion..
        /// </summary>
        public static string No_products_selected_for_deletion_ {
            get {
                return ResourceManager.GetString("No products selected for deletion.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No products selected for showing features..
        /// </summary>
        public static string No_products_selected_for_showing_features_ {
            get {
                return ResourceManager.GetString("No products selected for showing features.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No members to register..
        /// </summary>
        public static string NoMembers {
            get {
                return ResourceManager.GetString("NoMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoMemberSelected.
        /// </summary>
        public static string NoMemberSelected {
            get {
                return ResourceManager.GetString("NoMemberSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoUsersFound.
        /// </summary>
        public static string NoUsersFound {
            get {
                return ResourceManager.GetString("NoUsersFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OfficeLocation.
        /// </summary>
        public static string OfficeLocation {
            get {
                return ResourceManager.GetString("OfficeLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ok.
        /// </summary>
        public static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OrganizationNotFound.
        /// </summary>
        public static string OrganizationNotFound {
            get {
                return ResourceManager.GetString("OrganizationNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PasswordRequired.
        /// </summary>
        public static string PasswordRequired {
            get {
                return ResourceManager.GetString("PasswordRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Patients.
        /// </summary>
        public static string Patients {
            get {
                return ResourceManager.GetString("Patients", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /patients/.
        /// </summary>
        public static string PatientsPagePath {
            get {
                return ResourceManager.GetString("PatientsPagePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /patients/{0}.
        /// </summary>
        public static string PatientsPathTemplate {
            get {
                return ResourceManager.GetString("PatientsPathTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to pause.
        /// </summary>
        public static string Pause {
            get {
                return ResourceManager.GetString("Pause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PhoneNumber.
        /// </summary>
        public static string PhoneNumber {
            get {
                return ResourceManager.GetString("PhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to play_arrow.
        /// </summary>
        public static string Play {
            get {
                return ResourceManager.GetString("Play", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a unique facility name..
        /// </summary>
        public static string Please_provide_a_unique_facility_name_ {
            get {
                return ResourceManager.GetString("Please provide a unique facility name.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a unique role name..
        /// </summary>
        public static string Please_provide_a_unique_role_name_ {
            get {
                return ResourceManager.GetString("Please provide a unique role name.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PostalCode.
        /// </summary>
        public static string PostalCode {
            get {
                return ResourceManager.GetString("PostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while registering members..
        /// </summary>
        public static string PostLogError {
            get {
                return ResourceManager.GetString("PostLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingDeletedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingDeletedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingDeletedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingDeletionFailed.
        /// </summary>
        public static string PreDefinedPageRoleMappingDeletionFailed {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingDeletionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingUpdatedSuccessfully.
        /// </summary>
        public static string PreDefinedPageRoleMappingUpdatedSuccessfully {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingUpdatedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreDefinedPageRoleMappingUpdateFailed.
        /// </summary>
        public static string PreDefinedPageRoleMappingUpdateFailed {
            get {
                return ResourceManager.GetString("PreDefinedPageRoleMappingUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRODUCT.
        /// </summary>
        public static string PRODUCT {
            get {
                return ResourceManager.GetString("PRODUCT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product grid is not initialized..
        /// </summary>
        public static string Product_grid_is_not_initialized_ {
            get {
                return ResourceManager.GetString("Product grid is not initialized.", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to productId.
        /// </summary>
        public static string productId {
            get {
                return ResourceManager.GetString("productId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to retrieve products
        ///.
        /// </summary>
        public static string ProductRetrievalFailure {
            get {
                return ResourceManager.GetString("ProductRetrievalFailure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string Products {
            get {
                return ResourceManager.GetString("Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RecordsUrl.
        /// </summary>
        public static string RecordsUrl {
            get {
                return ResourceManager.GetString("RecordsUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RedirectUrl.
        /// </summary>
        public static string RedirectUrl {
            get {
                return ResourceManager.GetString("RedirectUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration Failed.
        /// </summary>
        public static string RegistrationFailed {
            get {
                return ResourceManager.GetString("RegistrationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SaveChanges.
        /// </summary>
        public static string SaveChanges {
            get {
                return ResourceManager.GetString("SaveChanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User details saved successfully..
        /// </summary>
        public static string Saved {
            get {
                return ResourceManager.GetString("Saved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchByEmail.
        /// </summary>
        public static string SearchByEmail {
            get {
                return ResourceManager.GetString("SearchByEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search by Username.
        /// </summary>
        public static string SearchUsername {
            get {
                return ResourceManager.GetString("SearchUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SearchUsers.
        /// </summary>
        public static string SearchUsers {
            get {
                return ResourceManager.GetString("SearchUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SelectSingleUser.
        /// </summary>
        public static string SelectSingleUser {
            get {
                return ResourceManager.GetString("SelectSingleUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign Out.
        /// </summary>
        public static string SignOut {
            get {
                return ResourceManager.GetString("SignOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to state.
        /// </summary>
        public static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stats.
        /// </summary>
        public static string Stats {
            get {
                return ResourceManager.GetString("Stats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StatsDetails.
        /// </summary>
        public static string StatsDetails {
            get {
                return ResourceManager.GetString("StatsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to stop_circle.
        /// </summary>
        public static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to streetAddress.
        /// </summary>
        public static string StreetAddress {
            get {
                return ResourceManager.GetString("StreetAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit.
        /// </summary>
        public static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Members registered successfully..
        /// </summary>
        public static string SuccessfulRegistration {
            get {
                return ResourceManager.GetString("SuccessfulRegistration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request successful.
        /// </summary>
        public static string SuccessfulRequest {
            get {
                return ResourceManager.GetString("SuccessfulRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to surname.
        /// </summary>
        public static string Surname {
            get {
                return ResourceManager.GetString("Surname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya Health.
        /// </summary>
        public static string TeyaHealth {
            get {
                return ResourceManager.GetString("TeyaHealth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teya Recorder.
        /// </summary>
        public static string TeyaRecorder {
            get {
                return ResourceManager.GetString("TeyaRecorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error updating profile..
        /// </summary>
        public static string UpdateError {
            get {
                return ResourceManager.GetString("UpdateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update user profile..
        /// </summary>
        public static string UpdateFailed {
            get {
                return ResourceManager.GetString("UpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log not updated.
        /// </summary>
        public static string UpdateLogError {
            get {
                return ResourceManager.GetString("UpdateLogError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Successful.
        /// </summary>
        public static string UpdateSuccessful {
            get {
                return ResourceManager.GetString("UpdateSuccessful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /updateuser.
        /// </summary>
        public static string UpdateUserPath {
            get {
                return ResourceManager.GetString("UpdateUserPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User License Management.
        /// </summary>
        public static string User_License_Management {
            get {
                return ResourceManager.GetString("User License Management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserAlreadyExists.
        /// </summary>
        public static string UserAlreadyExists {
            get {
                return ResourceManager.GetString("UserAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreated.
        /// </summary>
        public static string UserCreated {
            get {
                return ResourceManager.GetString("UserCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreatedSuccess.
        /// </summary>
        public static string UserCreatedSuccess {
            get {
                return ResourceManager.GetString("UserCreatedSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreationError.
        /// </summary>
        public static string UserCreationError {
            get {
                return ResourceManager.GetString("UserCreationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserCreationFailed.
        /// </summary>
        public static string UserCreationFailed {
            get {
                return ResourceManager.GetString("UserCreationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserDataNotLoaded.
        /// </summary>
        public static string UserDataNotLoaded {
            get {
                return ResourceManager.GetString("UserDataNotLoaded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserEmailAddress.
        /// </summary>
        public static string UserEmailAddress {
            get {
                return ResourceManager.GetString("UserEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserID.
        /// </summary>
        public static string UserID {
            get {
                return ResourceManager.GetString("UserID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInformationForm.
        /// </summary>
        public static string UserInformationForm {
            get {
                return ResourceManager.GetString("UserInformationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInformationFormHeader.
        /// </summary>
        public static string UserInformationFormHeader {
            get {
                return ResourceManager.GetString("UserInformationFormHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserInvitedSuccessfully.
        /// </summary>
        public static string UserInvitedSuccessfully {
            get {
                return ResourceManager.GetString("UserInvitedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserManagement.
        /// </summary>
        public static string UserManagementHeading {
            get {
                return ResourceManager.GetString("UserManagementHeading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to /usermanagement.
        /// </summary>
        public static string UserManagementRoute {
            get {
                return ResourceManager.GetString("UserManagementRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserName.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to preferred_username.
        /// </summary>
        public static string UsernameClaim {
            get {
                return ResourceManager.GetString("UsernameClaim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UsernameRequired.
        /// </summary>
        public static string UsernameRequired {
            get {
                return ResourceManager.GetString("UsernameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UserPrincipalName.
        /// </summary>
        public static string UserPrincipalName {
            get {
                return ResourceManager.GetString("UserPrincipalName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User profile updated successfully..
        /// </summary>
        public static string UserProfileUpdated {
            get {
                return ResourceManager.GetString("UserProfileUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to userType.
        /// </summary>
        public static string UserType {
            get {
                return ResourceManager.GetString("UserType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to welcome to teya.
        /// </summary>
        public static string wel {
            get {
                return ResourceManager.GetString("wel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome to Teya Web App!.
        /// </summary>
        public static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Who.
        /// </summary>
        public static string Who {
            get {
                return ResourceManager.GetString("Who", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
