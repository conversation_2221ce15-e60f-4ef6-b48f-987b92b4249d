using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Layout
{
    public partial class AdminNav : ComponentBase
    {
        private readonly string Chart = "/Chart";
        private readonly string Appointments = "/Appointments";
        private readonly string OrderSets = "/OrderSets";
        private readonly string Message = "/Message";
        private readonly string ReviewSoap = "/ReviewSoap";
        private readonly string ReviewRequests = "/review-requests";
        private readonly string MyRequests = "/my-requests";
        private readonly string Practice = "/Practice";
        private readonly string Document = "/Document";
        private readonly string Patients = "/Patients";
        private readonly string PlanBilling = "/PlanBilling";
        private readonly string Staff = "/Staff";
        private readonly string LicenseActivation = "/LicenseActivation";
        private readonly string License = "/License";
        private readonly string ProductFeatureSettings = "/ProductFeatureSettings";
        private readonly string Security = "/Security";
        private readonly string UserManagement = "/UserManagement";
        private readonly string Templates = "/Templates";
        private readonly string Config = "/Config";
        private readonly string About = "/about";

        private readonly string ClaimsLookup = "/ClaimsLookup";
        private readonly string BillingEncounters = "/BillingEncounters";
        private readonly string PatientDentalClaims = "/DentalClaims";

        private const string EHR_PRODUCT = "EHR";
        private const string BILLING_PRODUCT = "Billing";
        private const string adminRole = "Admin";
        private const string mailId = "@teyahealth.com";

        private List<string> AvailableProducts = new();
        private bool HasMultipleProducts = false;

        private static readonly Dictionary<string, HashSet<string>> ProductPageMappings = new()
        {
            [EHR_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/Chart", "/Appointments", "/OrderSets", "/Message", "/ReviewSoap", "/Practice",
                "/Document", "/Patients", "/PlanBilling", "/Staff",
                "/LicenseActivation", "/License", "/ProductFeatureSettings", "/Security",
                "/UserManagement", "/Templates", "/Config", "/About"
            },
            [BILLING_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/ClaimsLookup","/DentalClaims", "/BillingEncounters"
            }
        };

        private bool ShouldRenderLicenseLink = false;
        private List<ProductOrganizationMapping> UserProductMappings = new();
        private List<Product> AllProducts = new();
        private TeyaUIModels.Model.Organization CurrentOrganization = null;
        private bool HasEHRAccess = false;
        private bool HasBillingAccess = false;
        [Parameter] public bool IsDrawerOpen { get; set; }
        [Inject] private IMemberService MemberService { get; set; } = default!;
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; } = default!;
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPreDefinedPageRoleMappingService PreDefinedPageRoleMappingService { get; set; } = default!;
        [Inject] private ILogger<AdminNav> Logger { get; set; } = default!;
        [Inject] private RoleMappingState RoleMappingState { get; set; }
        [Inject] private ActiveUser activeUser { get; set; }
        private bool Subscription = false;
        [Inject] private IProductOrganizationMappingService ProductOrganizationMappingService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        private List<Member> members = new();
        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PreDefinedPageRoleMappingData> PreDefinedPageRoleMappings = new();

        [Parameter] public string SelectedProduct { get; set; } = "EHR";

        public bool HasEHRProductAccess() => HasEHRAccess;
        public bool HasBillingProductAccess() => HasBillingAccess;
        public bool ShouldShowProductSwitcher() => HasMultipleProducts;
        public string GetSelectedProduct() => SelectedProduct;
        public bool IsEHRSelected() => SelectedProduct == "EHR";
        public bool IsBillingSelected() => SelectedProduct == "Billing";
        private bool _isLoading = true;
        private bool _showNoProductMessage = false;
        [Inject] private UserContext usercontext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; } = default!;
        private bool ActiveUserSubscription { get; set; }
        private Guid ActiveUserOrgID { get; set; }
        private int PendingRequestCount { get; set; } = 0;
        private Timer _refreshTimer;

        protected override async Task OnInitializedAsync()
        {
            ActiveUserSubscription = usercontext.ActiveUserSubscription;
            ActiveUserOrgID = usercontext.ActiveUserOrganizationID;
            await LoadProductsAndMappingsAsync();
            await LoadPageRoleMappingsAsync();

            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

            if (queryParams.TryGetValue("product", out var productFromQuery))
            {
                SelectedProduct = productFromQuery.ToString();
            }

            await InitializeProductSwitcher();

            RoleMappingState.OnChange += async () =>
            {
                await LoadPageRoleMappingsAsync();
                await InvokeAsync(StateHasChanged);
            };

            await CheckUserAccess();
            await RefreshPendingRequestCount();

            // Set up timer to refresh pending request count every 30 seconds
            _refreshTimer = new Timer(async _ => await RefreshPendingRequestCount(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

            _isLoading = false;
            _showNoProductMessage = false;

            StateHasChanged();
        }

        private async Task LoadProductsAndMappingsAsync()
        {
            try
            {
                AllProducts = await ProductService.GetProductsAsync() ?? new List<Product>();

                if (!string.IsNullOrEmpty(activeUser.OrganizationName))
                {
                    var organizationId = await OrganizationService.GetOrganizationIdByNameAsync(activeUser.OrganizationName);
                    if (organizationId != Guid.Empty)
                    {
                        CurrentOrganization = await OrganizationService.GetOrganizationByIdAsync(organizationId);

                        var allMappings = await ProductOrganizationMappingService.GetAllProductOrganizationMappingsAsync();
                        UserProductMappings = allMappings?.Where(m => m.OrganizationId == organizationId && m.IsActive).ToList()
                                           ?? new List<ProductOrganizationMapping>();

                        await DetermineProductAccess();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading products and mappings: {ex.Message}");
            }
        }

        private async Task DetermineProductAccess()
        {
            try
            {
                var ehrProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(EHR_PRODUCT, StringComparison.OrdinalIgnoreCase));
                var billingProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(BILLING_PRODUCT, StringComparison.OrdinalIgnoreCase));

                if (ehrProduct != null)
                {
                    HasEHRAccess = UserProductMappings.Any(m => m.ProductId == ehrProduct.Id);
                }

                if (billingProduct != null)
                {
                    HasBillingAccess = UserProductMappings.Any(m => m.ProductId == billingProduct.Id);
                }

                if (!HasEHRAccess && !HasBillingAccess)
                {
                    HasEHRAccess = true;
                    Logger.LogInformation("No product mappings found, defaulting to EHR access");
                }

                Logger.LogInformation($"Product Access - EHR: {HasEHRAccess}, Billing: {HasBillingAccess}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error determining product access: {ex.Message}");
                HasEHRAccess = true;
            }
        }

        private async Task InitializeProductSwitcher()
        {
            AvailableProducts.Clear();

            if (HasEHRAccess)
            {
                AvailableProducts.Add(EHR_PRODUCT);
            }

            if (HasBillingAccess)
            {
                AvailableProducts.Add(BILLING_PRODUCT);
            }

            HasMultipleProducts = AvailableProducts.Count > 1;

            if (string.IsNullOrEmpty(SelectedProduct) || !AvailableProducts.Contains(SelectedProduct))
            {
                SelectedProduct = EHR_PRODUCT;
            }

            Logger.LogInformation($"Available Products: {string.Join(", ", AvailableProducts)}");
            Logger.LogInformation($"Selected Product: {SelectedProduct}");
            Logger.LogInformation($"Has Multiple Products: {HasMultipleProducts}");
        }

        private async Task CheckUserAccess()
        {
            if (activeUser?.mail?.EndsWith(mailId) == true)
            {
                ShouldRenderLicenseLink = true;
            }
        }

        private async Task LoadPageRoleMappingsAsync()
        {
            var activeUserOrganizationId = ActiveUserOrgID;
            Subscription = ActiveUserSubscription;
            try
            {
                members = await MemberService.GetAllMembersAsync(activeUserOrganizationId, Subscription);
                var matchingMember = members.FirstOrDefault(m => m.Id == Guid.Parse(activeUser.id));

                if (matchingMember != null)
                {
                    activeUser.role = matchingMember.RoleName;
                    if (matchingMember.RoleName != adminRole)
                    {
                        var preDefinedMappings = await PreDefinedPageRoleMappingService.GetPagesByRoleNameAsync(matchingMember.RoleName);
                        PreDefinedPageRoleMappings = preDefinedMappings?.ToList() ?? new List<PreDefinedPageRoleMappingData>();
                    }
                    else if (matchingMember != null && matchingMember.RoleID.HasValue)
                    {
                        var pageRoleMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(matchingMember.RoleID.Value, activeUserOrganizationId, Subscription);
                        PageRoleMappings = pageRoleMappings?.ToList() ?? new List<PageRoleMappingData>();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error during data loading: {ex.Message}");
            }

        }

        public void Dispose()
        {
            RoleMappingState.OnChange -= async () => await InvokeAsync(StateHasChanged);
            _refreshTimer?.Dispose();
        }

        private bool IsPageAccessible(string pageUrl)
        {
            if (!IsPageForSelectedProduct(pageUrl))
            {
                return false;
            }

            if (activeUser.role == "Admin")
            {
                return PageRoleMappings != null && PageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
            else
            {
                return PreDefinedPageRoleMappings != null && PreDefinedPageRoleMappings.Any(p => string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));
            }
        }

        private bool IsPageForSelectedProduct(string pageUrl)
        {
            if (string.IsNullOrEmpty(SelectedProduct))
                return false;

            return ProductPageMappings.TryGetValue(SelectedProduct, out var pages) && pages.Contains(pageUrl);
        }

        private string GetProductIcon(string product)
        {
            return product switch
            {
                "EHR" => Icons.Material.Filled.LocalHospital,
                "Billing" => Icons.Material.Filled.Receipt,
                _ => Icons.Material.Filled.Apps
            };
        }

        private string GetProductDisplayName(string product)
        {
            return product switch
            {
                "EHR" => Localizer["EHRProduct"],
                "Billing" => Localizer["BillingProduct"],
                _ => product
            };
        }

        private bool ShouldShowEHRSettings()
        {
            return IsPageAccessible(Practice) || IsPageAccessible(Patients) || IsPageAccessible(Staff) ||
                   IsPageAccessible(LicenseActivation) || IsPageAccessible(Security) || IsPageAccessible(UserManagement) ||
                   IsPageAccessible(Templates) || IsPageAccessible(Config) || IsPageAccessible(PlanBilling);
        }

        private void NavigateToPage(string url)
        {
            NavigationManager.NavigateTo(url);
        }

        private async Task RefreshPendingRequestCount()
        {
            try
            {
                if (Guid.TryParse(activeUser.id, out var userId))
                {
                    var count = await CosigningRequestService.GetPendingRequestCountAsync(userId, ActiveUserOrgID, ActiveUserSubscription);
                    if (count != PendingRequestCount)
                    {
                        PendingRequestCount = count;
                        await InvokeAsync(StateHasChanged);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error refreshing pending request count");
            }
        }

        private string GetReviewRequestIcon()
        {
            return PendingRequestCount > 0 ? Icons.Material.Filled.RateReview : Icons.Material.Outlined.RateReview;
        }

        private Color GetReviewRequestColor()
        {
            return PendingRequestCount > 0 ? Color.Success : Color.Inherit;
        }

        private string GetReviewRequestTooltip()
        {
            return PendingRequestCount > 0
                ? $"Review Requests ({PendingRequestCount} pending)"
                : "Review Requests";
        }


    }
}