﻿using System;
using System.Collections.Generic;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using DotNetEnv;

namespace TeyaUIViewModels.ViewModel
{
    public class InsuranceService : IInsuranceService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<InsuranceService> _logger;
        private readonly IStringLocalizer<InsuranceService> _localizer;
        private readonly string _MemberService;

        public InsuranceService(HttpClient httpClient, ILogger<InsuranceService> logger, IStringLocalizer<InsuranceService> localizer)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        public async Task<Insurance> GetInsuranceByIdAsync(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<Insurance>($"{_MemberService}/api/insurance/{id}/{OrgID}/{Subscription}");
                return response ?? throw new InvalidOperationException(_localizer["Insurance not found"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching insurance by ID"]);
                throw;
            }
        }

        public async Task<List<Insurance>> GetAllInsurancesAsync()
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<Insurance>>($"{_MemberService}/api/insurance");
                return response ?? new List<Insurance>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error fetching all insurances"]);
                throw;
            }
        }

        public async Task<bool> AddInsuranceAsync(Insurance insurance)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_MemberService}/api/insurance", insurance);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error adding insurance"]);
                return false;
            }
        }

        public async Task<bool> UpdateInsuranceAsync(Guid id, Insurance insurance)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_MemberService}/api/insurance/{id}", insurance);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error updating insurance"]);
                return false;
            }
        }

        public async Task<bool> DeleteInsuranceAsync(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_MemberService}/api/insurance/{id}/{OrgID}/{Subscription}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["Error deleting insurance"]);
                return false;
            }
        }
    }
}
