﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PrescriptionMedicationService : IPrescriptionMedicationService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public PrescriptionMedicationService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all medications, both active and inactive
        /// </summary>

        public async Task<List<ActiveMedication>> GetMedicationsByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ActiveMedication>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Get only Active Medications
        /// </summary>

        public async Task<List<ActiveMedication>> GetMedicationsByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/{id}/isActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<ActiveMedication>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add New list of medications
        /// </summary>

        public async Task AddMedicationAsync(List<ActiveMedication> medicines, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/AddMedication/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicines);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Delete medication 
        /// </summary>

        public async Task DeletemedicationAsync(ActiveMedication medicine, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicine);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update single medication
        /// </summary>
        /// <param name="medicines"></param>
        /// <returns></returns>
        public async Task UpdateMedicationAsync(ActiveMedication medicines, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/{medicines.PatientId}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicines);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Update List of medication for Batch update
        /// </summary>

        public async Task UpdateMedicationListAsync(List<ActiveMedication> medicines, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/PrescriptionMedication/UpdateMedicationList/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(medicines);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }
    }

}

