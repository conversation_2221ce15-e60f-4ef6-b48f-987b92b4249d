﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class FDBVaccines : IModel
    {
        public string EVD_CVX_CD { get; set; }
        public string EVD_CVX_CD_DESC_SHORT { get; set; }
        public string EVD_CVX_CD_DESC_LONG { get; set; }
        public int EVD_CVX_CD_USAGE { get; set; }
        public string EVD_CVX_CODE_STATUS { get; set; }
        public bool EVD_CVX_CD_NONVACCINE { get; set; }
        public string EVD_CVX_CD_ADD_DT { get; set; }
        public string EVD_CVX_CD_OBS_DT { get; set; }
        public string EVD_CVX_LAST_UPDATE_DT { get; set; }
    }
}
