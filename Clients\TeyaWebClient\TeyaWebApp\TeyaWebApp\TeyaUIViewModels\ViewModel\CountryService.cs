﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaWebApp.Services
{
    public class countryService : ICountryService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<countryService> _localizer;
        private readonly ILogger<countryService> _logger;

        public countryService(HttpClient httpClient, IStringLocalizer<countryService> localizer, ILogger<countryService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
            _tokenService = tokenService;
        }

        public async Task<IEnumerable<Country>> GetAllCountriesAsync()
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_MemberService}/api/Country";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<Country>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllCountries"]);
                throw;
            }
        }

        public async Task<Country> GetCountryByIdAsync(Guid id)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Country/{id}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<Country>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetCountryByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingCountryById"], id);
                throw;
            }
        }

        public async Task<IEnumerable<Country>> GetCountriesByNameAsync(string name)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/Country/search?name={Uri.EscapeDataString(name)}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<IEnumerable<Country>>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetCountriesByNameFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingCountriesByName"], name);
                throw;
            }
        }

        public async Task AddCountryAsync(Country country)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = JsonSerializer.Serialize(country);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/Country";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["CountryAddedSuccessfully"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddCountryFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingCountry"]);
                throw;
            }
        }

        public async Task UpdateCountryAsync(Country country)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_MemberService}/api/Country/{country.CountryId}";
                var bodyContent = JsonSerializer.Serialize(country);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["CountryUpdatedSuccessfully"], country.CountryId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["CountryUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingCountry"]);
                throw;
            }
        }

        public async Task DeleteCountryByIdAsync(Guid id)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_MemberService}/api/Country/{id}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["CountryDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["CountryDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingCountry"], id);
                throw;
            }
        }
    }
}
