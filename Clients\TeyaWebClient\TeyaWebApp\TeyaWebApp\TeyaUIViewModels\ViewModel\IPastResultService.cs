﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPastResultService
    {
        Task<List<PastResult>> GetResultsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddResultAsync(List<PastResult> previous, Guid? OrgID, bool Subscription);
        Task DeletePastResultAsync(PastResult pastres, Guid? OrgID, bool Subscription);
        Task UpdateResultAsync(PastResult past, Guid? OrgID, bool Subscription);
        Task<List<PastResult>> GetResultByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task UpdatePastResultListAsync(List<PastResult> Pastres, Guid? OrgID, bool Subscription);
    }
}
