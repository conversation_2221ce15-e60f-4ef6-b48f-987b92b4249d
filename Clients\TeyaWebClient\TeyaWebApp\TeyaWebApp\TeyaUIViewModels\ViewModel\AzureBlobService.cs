﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Components.Forms;
using System;
using System.IO;
using System.Threading.Tasks;

namespace TeyaUIViewModels.ViewModel
{
    public class AzureBlobService
    {
        private readonly string connectionString;
        private readonly string containerName;

        public AzureBlobService()
        {
            connectionString = Environment.GetEnvironmentVariable("AZURE-BLOB-CONNECTION-STRING");
            containerName = Environment.GetEnvironmentVariable("AZURE-BLOB-CONTAINER-NAME");
        }

        public async Task<string> UploadImageAsync(IBrowserFile file)
        {
            BlobServiceClient blobServiceClient = new BlobServiceClient(connectionString);
            BlobContainerClient containerClient = blobServiceClient.GetBlobContainerClient(containerName);
            await containerClient.CreateIfNotExistsAsync();

            string fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name)}";
            BlobClient blobClient = containerClient.GetBlobClient(fileName);

            using (var stream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024))
            {
                await blobClient.UploadAsync(stream, true);
            }

            return blobClient.Uri.ToString();
        }
    }
}
