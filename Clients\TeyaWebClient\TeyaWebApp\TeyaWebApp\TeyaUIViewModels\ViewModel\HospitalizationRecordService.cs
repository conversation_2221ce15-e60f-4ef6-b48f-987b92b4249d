﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public class HospitalizationRecordService : IHospitalizationRecordService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<HospitalizationRecordService> _localizer;
        private readonly ILogger<HospitalizationRecordService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public HospitalizationRecordService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<HospitalizationRecordService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active HospitalizationRecord by Id 
        /// </summary>
        public async Task<List<HospitalizationRecord>> GetHospitalizationRecordByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/{id}/isActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<HospitalizationRecord>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Get All HospitalizationRecord by Id 
        /// </summary>
        public async Task<List<HospitalizationRecord>> GetHospitalizationRecordAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<HospitalizationRecord>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["MemberRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add new HospitalizationRecord
        /// </summary>
        public async Task CreateHospitalizationRecordAsync(List<HospitalizationRecord> hospitalizationRecord, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(hospitalizationRecord);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/{OrgID}/{Subscription}";

            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };

            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["HospitalizationRecordRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update an existing HospitalizationRecord
        /// </summary>
        public async Task UpdateHospitalizationRecordAsync(HospitalizationRecord hospitalizationRecord, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/{hospitalizationRecord.RecordID}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(hospitalizationRecord);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        ///  Delete an existing HospitalizationRecord By Id
        /// </summary>
        public async Task DeleteHospitalizationRecordAsync(Guid HospitalizationRecordId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/{HospitalizationRecordId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }

        /// <summary>
        ///  Update an existing List of HospitalizationRecord
        /// </summary>
        public async Task UpdateHospitalizationRecordList(List<HospitalizationRecord> hospitalizationRecord, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/HospitalizationRecord/updateHospitalizationRecord/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var bodyContent = System.Text.Json.JsonSerializer.Serialize(hospitalizationRecord);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateHospitalizationRecordListFailure"], ex);
            }
        }
    }
}