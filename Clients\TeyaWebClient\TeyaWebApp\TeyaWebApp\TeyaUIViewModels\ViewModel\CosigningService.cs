﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class CosigningService : ICosigningService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _encounterNotesUrl;
        private readonly ITokenService _tokenService;

        public CosigningService(
            HttpClient httpClient,
            IConfiguration configuration,
            IStringLocalizer<TeyaUIViewModelsStrings> localizer,
            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _tokenService = tokenService;
            _encounterNotesUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task<IEnumerable<Cosigning>> GetCosigningsByRecordId(Guid recordId)
        {
            // For backward compatibility, use default values
            return await GetCosigningsByRecordId(recordId, Guid.Empty, false);
        }

        public async Task<IEnumerable<Cosigning>> GetCosigningsByRecordId(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                // Use the correct endpoint that matches the backend controller
                var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/RecordId/{recordId}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<IEnumerable<Cosigning>>() ?? Enumerable.Empty<Cosigning>();
                }

                throw new HttpRequestException($"Failed to get cosignings for record {recordId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error retrieving cosignings for record {recordId}: {ex.Message}", ex);
            }
        }

        public async Task AddCosigning(List<Cosigning> cosignings)
        {
            if (cosignings == null || !cosignings.Any())
            {
                throw new ArgumentException("Cosignings list cannot be null or empty");
            }

            // Use the first cosigning's organization info for the API call
            var firstCosigning = cosignings.First();
            await AddCosigning(cosignings, firstCosigning.OrganizationId, firstCosigning.Subscription);
        }

        public async Task AddCosigning(List<Cosigning> cosignings, Guid organizationId, bool subscription)
        {
            try
            {
                if (cosignings == null || !cosignings.Any())
                {
                    throw new ArgumentException("Cosignings list cannot be null or empty");
                }

                var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/add/{organizationId}/{subscription}";
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(cosignings), Encoding.UTF8, "application/json")
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to add cosignings. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error adding cosignings: {ex.Message}", ex);
            }
        }

        public async Task UpdateCosigning(Cosigning cosigning)
        {
            try
            {
                if (cosigning == null)
                {
                    throw new ArgumentNullException(nameof(cosigning), "Cosigning object cannot be null");
                }

                if (cosigning.Id == Guid.Empty)
                {
                    throw new ArgumentException("Cosigning ID cannot be empty", nameof(cosigning));
                }

                // Use the correct endpoint pattern that matches the backend
                var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/update/{cosigning.Id}/{cosigning.OrganizationId}/{cosigning.Subscription}";
                var request = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(cosigning), Encoding.UTF8, "application/json")
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Failed to update cosigning {cosigning.Id}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}, Details: {errorContent}");
                }
            }
            catch (Exception ex) when (!(ex is ArgumentException || ex is ArgumentNullException || ex is HttpRequestException))
            {
                throw new InvalidOperationException($"Error updating cosigning {cosigning?.Id}: {ex.Message}", ex);
            }
        }

        public async Task<Cosigning> GetCosigningStatus(Guid recordId)
        {
            try
            {
                var apiUrl = $"{_encounterNotesUrl}/api/Cosigning/status/{recordId}";
                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _tokenService.AccessToken);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<Cosigning>() ?? new Cosigning();
                }

                throw new HttpRequestException($"Failed to get cosigning status for record {recordId}. Status: {response.StatusCode}, Reason: {response.ReasonPhrase}");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error retrieving cosigning status for record {recordId}: {ex.Message}", ex);
            }
        }



    }
}