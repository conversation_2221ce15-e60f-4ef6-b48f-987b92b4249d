﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IReferralOutgoingService
    {
        Task<List<PatientReferralOutgoing>> GetReferralOutgoingsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<PatientReferralOutgoing>> GetReferralOutgoingsByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task AddReferralOutgoingAsync(List<PatientReferralOutgoing> ReferralOutgoings, Guid? OrgID, bool Subscription);
        Task DeleteReferralOutgoingAsync(PatientReferralOutgoing ReferralOutgoing, Guid? OrgID, bool Subscription);
        Task UpdateReferralOutgoingAsync(PatientReferralOutgoing ReferralOutgoing, Guid? OrgID, bool Subscription);
        Task UpdateReferralOutgoingsListAsync(List<PatientReferralOutgoing> ReferralOutgoings, Guid? OrgID, bool Subscription);
    }
}
