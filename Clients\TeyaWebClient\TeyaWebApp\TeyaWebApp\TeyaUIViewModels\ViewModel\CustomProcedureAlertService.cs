﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class CustomProcedureAlertService : ICustomProcedureAlertService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _AlertsServiceURL;
        private readonly ITokenService _tokenService;

        public CustomProcedureAlertService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _AlertsServiceURL = Environment.GetEnvironmentVariable("AlertsServiceURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all CustomLabAlerts (Active & InActive)
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>List of CustomLabAlerts</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<CustomProcedureAlerts>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts/{id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CustomProcedureAlerts>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Get active CustomLabAlerts
        /// </summary>
        /// <param name="id">Patient ID</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>List of active CustomLabAlerts</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task<List<CustomProcedureAlerts>> GetActiveCustomProcedureAlertsByOrganizationIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts/{OrgID}/active";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CustomProcedureAlerts>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["RecordNotFound"]);
            }
        }

        /// <summary>
        /// Add a list of CustomLabAlerts
        /// </summary>
        /// <param name="customLabAlerts">List of CustomLabAlerts to add</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task AddCustomProcedureAlertsAsync(List<CustomProcedureAlerts> customProcedureAlerts, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts";

            // Set IsActive to true for all new alerts
            foreach (var alert in customProcedureAlerts)
            {
                alert.IsActive = true;
            }

            var bodyContent = System.Text.Json.JsonSerializer.Serialize(customProcedureAlerts);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Update a CustomLabAlert
        /// </summary>
        /// <param name="customLabAlert">CustomLabAlert to update</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateCustomProcedureAlertAsync(CustomProcedureAlerts customProcedureAlert, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts/{customProcedureAlert.Id}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(customProcedureAlert);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        /// Update a list of CustomLabAlerts
        /// </summary>
        /// <param name="customLabAlerts">List of CustomLabAlerts to update</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task UpdateCustomProcedureAlertsListAsync(List<CustomProcedureAlerts> customProcedureAlerts, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(customProcedureAlerts);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }

        /// <summary>
        /// Delete a CustomLabAlert
        /// </summary>
        /// <param name="customLabAlert">CustomLabAlert to delete</param>
        /// <param name="OrgID">Organization ID</param>
        /// <param name="Subscription">Subscription flag</param>
        /// <returns>Task</returns>
        /// <exception cref="HttpRequestException"></exception>
        public async Task DeleteCustomProcedureAlertByEntityAsync(CustomProcedureAlerts customProcedureAlert, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_AlertsServiceURL}/api/CustomProcedureAlerts/{customProcedureAlert.Id}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(_localizer["DatabaseError"]);
            }
        }
    }
}
