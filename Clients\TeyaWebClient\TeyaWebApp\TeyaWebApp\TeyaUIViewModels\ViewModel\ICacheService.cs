﻿using StackExchange.Redis;
using System.Text.Json;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICacheService
    {
        Task<List<FDBMedicationName>> GetFDBMedicationsAsync(Func<Task<List<FDBMedicationName>>> fetchFunc);
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getDataFunc, TimeSpan? expirationTime = null);
        Task RemoveAsync(string key);
        Task RemoveByPatternAsync(string pattern);
        Task<bool> KeyExistsAsync(string key);
        Task RefreshMedicationData();
    }

}

