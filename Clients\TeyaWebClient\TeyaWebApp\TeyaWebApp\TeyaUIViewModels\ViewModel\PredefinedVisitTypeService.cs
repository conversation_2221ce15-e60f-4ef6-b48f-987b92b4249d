﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DotNetEnv;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public class PredefinedVisitTypeService : IPredefinedVisitTypeService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _PredefinedVisitTypeService;
        private readonly IStringLocalizer<PredefinedVisitTypeService> _localizer;
        private readonly ILogger<PredefinedVisitTypeService> _logger;
        private bool HasAccess { get; set; }
        public PredefinedVisitTypeService(HttpClient httpClient, IStringLocalizer<PredefinedVisitTypeService> localizer, ILogger<PredefinedVisitTypeService> logger, ITokenService tokenService)
        {
            _tokenService = tokenService;
            Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _PredefinedVisitTypeService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Gets all PredefinedVisitTypes.
        /// </summary>
        public async Task<List<PredefinedVisitTypedata>> GetAllPredefinedVisitTypesAsync()
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError(_localizer["AccessTokenNotFound"]);
                    throw new UnauthorizedAccessException(_localizer["AccessTokenMissing"]);
                }

                var apiUrl = $"{_PredefinedVisitTypeService}/api/PredefinedVisitType";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                return JsonSerializer.Deserialize<List<PredefinedVisitTypedata>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllPredefinedVisitTypes"]);
                throw;
            }
        }

    }
}
