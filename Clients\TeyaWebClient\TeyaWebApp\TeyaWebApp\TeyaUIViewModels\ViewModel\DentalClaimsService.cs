﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class DentalClaimsService : IDentalClaimsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _Billing;
        private readonly ITokenService _tokenService;

        public DentalClaimsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _Billing = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all DentalClaims, both active and inactive
        /// </summary>
        public async Task<List<CompleteDentalClaims>> GetDentalClaimsByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/DentalClaims/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteDentalClaims>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add New list of DentalClaims
        /// </summary>
        public async Task AddDentalClaimsAsync(List<CompleteDentalClaims> completeDentalClaims, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/DentalClaims/Add/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(completeDentalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// Delete DentalClaim 
        /// </summary>
        public async Task DeleteDentalClaimsAsync(CompleteDentalClaims DentalClaims, Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/DentalClaims/{id}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(DentalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update single DentalClaim
        /// </summary>
        /// <param name = "DentalClaims" ></ param >
        /// < returns ></ returns >
        public async Task UpdateDentalClaimsAsync(CompleteDentalClaims DentalClaims, Guid? OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_Billing}/api/DentalClaims/{DentalClaims.Id}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(DentalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task<List<CompleteDentalClaims>> GetAllDentalClaimsAsync(Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/DentalClaims/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteDentalClaims>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

    }

}

