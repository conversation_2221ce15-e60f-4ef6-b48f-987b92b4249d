﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IVitalService
    {
        Task<List<PatientVitals>> GetVitalsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<PatientVitals>> GetVitalsByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task AddVitalAsync(List<PatientVitals> vitals, Guid? OrgID, bool Subscription);
        Task DeleteVitalAsync(PatientVitals vital, Guid? OrgID, bool Subscription);
        Task UpdateVitalAsync(PatientVitals vital, Guid? OrgID, bool Subscription);
        Task UpdateVitalsListAsync(List<PatientVitals> vitals, Guid? OrgID, bool Subscription);
    }
}
