﻿using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;

namespace TeyaUIModels.ViewModel
{
    public interface IProductOrganizationMappingService
    {
        Task<ProductOrganizationMapping> RegisterProductOrganizationMappingsAsync(ProductOrganizationMapping ProductOrganizationMapping);
        Task<ProductOrganizationMapping> GetProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId);
        Task<List<ProductOrganizationMapping>> GetMappingsByProductIdAsync(Guid productId);
        Task<List<ProductOrganizationMapping>> GetAllProductOrganizationMappingsAsync();
        Task DeleteProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId);
        Task UpdateProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId, ProductOrganizationMapping ProductOrganizationMapping);
    }
}