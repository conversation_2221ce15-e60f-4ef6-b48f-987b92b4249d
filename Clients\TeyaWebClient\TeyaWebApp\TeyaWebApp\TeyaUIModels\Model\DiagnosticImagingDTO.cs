﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class DiagnosticImagingDTO : IModel
    {
        public Guid RecordID { get; set; }
        public Guid? OrganizationID { get; set; }
        public Guid? PatientId { get; set; }
        public string? Status { get; set; }
        public string? Provider { get; set; }
        public string? Facility { get; set; }
        public string? AssignedTo { get; set; }
        public bool FutureOrder { get; set; }
        public bool HigherPriority { get; set; }
        public bool InHouse { get; set; }
        public string? Procedgure { get; set; }
        public DateTime? OrderDate { get; set; }
        public string? Reason { get; set; }
        public bool Recieved { get; set; }
        public DateTime? Date { get; set; }
        public string? Result { get; set; }
        public string? Notes { get; set; }
        public string? ClassicalInfo { get; set; }
        public string? InternalNotes { get; set; }
        public bool IsActive { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool Subscription { get; set; }
        public virtual ICollection<DiagnosticImagingAssessment>? Assessments { get; set; }
    }
}
