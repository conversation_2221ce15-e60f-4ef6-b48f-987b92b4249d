﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PastResultService : IPastResultService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _PastUrl;
        private readonly ITokenService _tokenService;

        public PastResultService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _PastUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        public async Task<List<PastResult>> GetResultsByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_PastUrl}/api/PastResults/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<PastResult>>();
                }
                else
                {
                    throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
                }
            }
        }

        public async Task AddResultAsync(List<PastResult> previous, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_PastUrl}/api/PastResults/AddResult/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(previous);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        public async Task DeletePastResultAsync(PastResult pastres, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_PastUrl}/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(pastres);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        public async Task UpdateResultAsync(PastResult past, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_PastUrl}/api/PastResults/{past.PatientId}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(past);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task<List<PastResult>> GetResultByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_PastUrl}/api/PastResults/{id}/isActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadFromJsonAsync<List<PastResult>>();
                }
                else
                {
                    throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
                }
            }
        }

        public async Task UpdatePastResultListAsync(List<PastResult> Pastres, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_PastUrl}/api/PastResults/UpdatePastList/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(Pastres);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }
    }
}
