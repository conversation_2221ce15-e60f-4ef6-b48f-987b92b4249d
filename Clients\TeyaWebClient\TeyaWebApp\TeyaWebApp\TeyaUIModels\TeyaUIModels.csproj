﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Version>1.0.50702.3</Version>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.10" />
    <PackageReference Include="Microsoft.Graph" Version="5.68.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Lists" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.Popups" Version="27.1.58" />
    <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.58" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

</Project>
