﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaWebApp.Services
{
    public interface IPatientSpecificAlertsService
    {
        /// <summary>
        /// Gets all patient specific alerts for a patient
        /// </summary>
        /// <param name="patientId">The patient ID</param>
        /// <returns>List of all patient specific alerts for the patient</returns>
        Task<IEnumerable<PatientSpecificAlertsData>> GetAllPatientSpecificAlertsAsync(Guid patientId);

        /// <summary>
        /// Gets all active patient specific alerts for a patient
        /// </summary>
        /// <param name="patientId">The patient ID</param>
        /// <returns>List of active patient specific alerts for the patient</returns>
        Task<IEnumerable<PatientSpecificAlertsData>> GetActivePatientSpecificAlertsAsync(Guid patientId);

        /// <summary>
        /// Adds new patient specific alerts
        /// </summary>
        /// <param name="alerts">The patient specific alerts to add</param>
        Task AddPatientSpecificAlertsAsync(List<PatientSpecificAlertsData> alerts);

        /// <summary>
        /// Adds a single patient specific alert
        /// </summary>
        /// <param name="alert">The patient specific alert to add</param>
        Task AddPatientSpecificAlertAsync(PatientSpecificAlertsData alert);

        /// <summary>
        /// Updates an existing patient specific alert
        /// </summary>
        /// <param name="alert">The patient specific alert to update</param>
        Task UpdatePatientSpecificAlertAsync(PatientSpecificAlertsData alert);

        /// <summary>
        /// Updates a list of patient specific alerts
        /// </summary>
        /// <param name="alerts">The list of patient specific alerts to update</param>
        Task UpdatePatientSpecificAlertsListAsync(List<PatientSpecificAlertsData> alerts);

        /// <summary>
        /// Deletes a patient specific alert by ID
        /// </summary>
        /// <param name="id">The ID of the patient specific alert to delete</param>
        Task DeletePatientSpecificAlertByIdAsync(Guid id);

        /// <summary>
        /// Deletes a patient specific alert by entity
        /// </summary>
        /// <param name="alert">The patient specific alert to delete</param>
        Task DeletePatientSpecificAlertByEntityAsync(PatientSpecificAlertsData alert);
    }
}