﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IRxNormService
    {
        Task<List<RxNormConcept>> GetAllRxNormMedications();
        Task<List<RxNormConcept>> GetAllRxNormMedicationsBySearchTerm(string searchterm);
        Task<List<RxNormConcept>> GetRxNormSBDCMedications(string str);
        Task<List<string>> GetAllBrandNames();
        Task<List<string>> GetSBDNamesAsync(string name);
        Task<List<string>> GetAllDrugNames();
        string GetRxcuiByName(string name);
    }
}