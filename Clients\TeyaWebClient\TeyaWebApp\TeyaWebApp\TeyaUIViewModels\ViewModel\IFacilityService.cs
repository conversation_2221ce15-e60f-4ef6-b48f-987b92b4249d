﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFacilityService
    {
        Task<Facility> RegisterFacilityAsync(Facility facility);
        Task<Facility> GetFacilityByIdAsync(Guid facilityId, Guid OrgID, bool Subscription);
        Task<List<Facility>> GetAllFacilitiesAsync(Guid orgId, bool Subscription);
        Task<List<string>> GetFacilityNamesAsync(Guid orgId, bool Subscription);
        Task DeleteFacilityByIdAsync(Guid facilityId, Guid OrgID, bool Subscription);
        Task UpdateFacilityByIdAsync(Guid facilityId, Facility facility);
        Task<List<Facility>> GetAllFacilitiesByOrgIdAsync(Guid? ID, bool Subscription);
    }
}
