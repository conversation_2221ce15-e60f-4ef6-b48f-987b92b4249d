﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    /// <summary>
    /// Service for retrieving patient chart data from an external API.
    /// </summary>
    public class ChartService : IChartService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _MemberService;
        private readonly ITokenService _tokenService;


        /// <summary>
        /// Initializes a new instance of the <see cref="ChartService"/> class.
        /// </summary>
        /// <param name="httpClient">The HTTP client used for API requests.</param>
        /// <param name="configuration">The application configuration settings.</param>
        /// <param name="localizer">The localizer for retrieving localized strings.</param>
        /// <param name="tokenService">The token service for authentication.</param>
        public ChartService(HttpClient httpClient, IConfiguration configuration,
                            IStringLocalizer<TeyaUIViewModelsStrings> localizer,
                            ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _tokenService = tokenService;
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        /// <summary>
        /// Retrieves a patient's data by their ID.
        /// </summary>
        /// <param name="id">The unique identifier of the patient.</param>
        /// <param name="str">Additional query string parameter.</param>
        /// <returns>The patient's details if found.</returns>
        /// <exception cref="HttpRequestException">Thrown when the request fails.</exception>
        public async Task<Patient> GetPatientByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            var apiUrl = $"{_MemberService}/Patient/{id}/{orgId}/{subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<Patient>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }
    }
}
