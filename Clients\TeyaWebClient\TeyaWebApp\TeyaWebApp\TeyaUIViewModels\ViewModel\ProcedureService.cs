﻿
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{

    public class ProcedureService : IProcedureService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _procedureUrl;
        private readonly ITokenService _tokenService;


        public ProcedureService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _procedureUrl = Environment.GetEnvironmentVariable("EncounterNotesURL");// Add url
            _tokenService = tokenService;
        }

        public async Task AddProcedureAsync(List<Procedures> procedure, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_procedureUrl}/api/Procedure/AddProcedure/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(procedure);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        public async Task<List<Procedures>> GetProcedureByPatientId(Guid patientId, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_procedureUrl}/api/Procedure/{patientId}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Procedures>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }

        }

        public async Task UpdateProcedureListAsync(List<Procedures> procedure, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_procedureUrl}/api/Procedure/UpdateProcedureList/{OrgID}/{Subscription}";
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(procedure);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task<List<Procedures>> LoadProcedureAsync(Guid patientId, Guid? OrgID, bool Subscription)
        {
            try
            {

                var result = await GetProcedureByPatientId(patientId, OrgID, false);


                return result.Select(h => new Procedures
                {
                    Id = h.Id,
                    PatientId = h.PatientId,
                    CPTCode = h.CPTCode,
                    Description = h.Description,
                    Notes = h.Notes,
                    OrderedBy = h.OrderedBy,
                    LastUpdatedDate = h.LastUpdatedDate,
                    CreatedByUserId = h.CreatedByUserId,
                    UpdatedByUserId = h.UpdatedByUserId,
                    OrderDate = h.OrderDate,
                    OrganizationId = h.OrganizationId,
                    PcpId = h.PcpId,
                    IsDeleted = h.IsDeleted,
                    AssessmentData = h.AssessmentData,
                    AssessmentId = h.AssessmentId,
                    ChiefComplaint=h.ChiefComplaint,
                    ChiefComplaintId=h.ChiefComplaintId
                }).ToList();
            }
            catch (Exception ex)
            {

                throw new Exception("Error loading OB histories: " + ex.Message);
            }
        }
    }
}
