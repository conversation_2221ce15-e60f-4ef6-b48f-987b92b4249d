﻿using DotNetEnv;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Net;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;


namespace TeyaWebApp.ViewModel
{
    public class ProductOrganizationMappingService : IProductOrganizationMappingService
    {
        private readonly HttpClient _httpClient;
        private readonly string _MemberService;
        private readonly IStringLocalizer<ProductOrganizationMappingService> _localizer;
        private readonly ILogger<ProductOrganizationMappingService> _logger;
        

        public ProductOrganizationMappingService(HttpClient httpClient, IStringLocalizer<ProductOrganizationMappingService> localizer, ILogger<ProductOrganizationMappingService> logger)
        {
            DotNetEnv.Env.Load();
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger ?? throw new ArgumentNullException(nameof(_logger));
            Env.Load();
            _MemberService = Environment.GetEnvironmentVariable("MemberServiceURL");
        }

        public async Task<ProductOrganizationMapping> RegisterProductOrganizationMappingsAsync(ProductOrganizationMapping ProductOrganizationMapping)
        {
            try
            {
                var bodyContent = JsonSerializer.Serialize(ProductOrganizationMapping);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);

                    try
                    {
                        return JsonSerializer.Deserialize<ProductOrganizationMapping>(responseData);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogError(ex, _localizer["JsonDeserializationError", ex.Message]);
                        throw;
                    }
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    _logger.LogError("ProductOrganizationMapping already exists with status code {StatusCode}", response.StatusCode);
                    throw new HttpRequestException(_localizer["ProductOrganizationMappingAlreadyExists"]);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Registration failed. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["RegistrationFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ProductOrganizationMappingRegistrationError"]);
                throw;
            }
        }

        public async Task<ProductOrganizationMapping> GetProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping/{ProductOrganizationMappingId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    return JsonSerializer.Deserialize<ProductOrganizationMapping>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProductOrganizationMappingById"], ProductOrganizationMappingId);
                throw;
            }
        }

        public async Task<List<ProductOrganizationMapping>> GetAllProductOrganizationMappingsAsync()
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                return JsonSerializer.Deserialize<List<ProductOrganizationMapping>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllProductOrganizationMappings"]);
                throw;
            }
        }

        public async Task DeleteProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping/{ProductOrganizationMappingId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["ProductOrganizationMappingDeletedSuccessfully"], ProductOrganizationMappingId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"DeleteFaileStatusCode: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["ProductOrganizationMappingDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingProductOrganizationMapping"], ProductOrganizationMappingId);
                throw;
            }
        }

        public async Task UpdateProductOrganizationMappingByIdAsync(Guid ProductOrganizationMappingId, ProductOrganizationMapping ProductOrganizationMapping)
        {
            if (ProductOrganizationMapping == null || ProductOrganizationMapping.ProductOrganizationMappingId != ProductOrganizationMappingId)
            {
                _logger.LogError(_localizer["InvalidProductOrganizationMapping"]);
                throw new ArgumentException(_localizer["InvalidProductOrganizationMapping"]);
            }

            try
            {
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping/{ProductOrganizationMappingId}";
                var bodyContent = JsonSerializer.Serialize(ProductOrganizationMapping);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Content = content;
                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["UpdateSuccessful"], ProductOrganizationMappingId);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["UpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingProductOrganizationMapping"], ProductOrganizationMappingId);
                throw;
            }
        }
        public async Task<List<ProductOrganizationMapping>> GetMappingsByProductIdAsync(Guid productId)
        {
            try
            {
                var apiUrl = $"{_MemberService}/api/ProductOrganizationMapping/product/{productId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                return JsonSerializer.Deserialize<List<ProductOrganizationMapping>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingMappingsByProductId"], productId);
                throw;
            }
        }
    }
}
