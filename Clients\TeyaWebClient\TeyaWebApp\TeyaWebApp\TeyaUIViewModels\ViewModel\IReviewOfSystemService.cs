﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IReviewOfSystemService
    {
        Task<List<ReviewOfSystem>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<ReviewOfSystem>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddReviewOfSystemAsync(List<ReviewOfSystem> reviewOfSystems, Guid? OrgID, bool Subscription);
        Task UpdateReviewOfSystemAsync(ReviewOfSystem reviewOfSystem, Guid? OrgID, bool Subscription);
        Task UpdateReviewOfSystemListAsync(List<ReviewOfSystem> reviewOfSystems, Guid? OrgID, bool Subscription);
        Task DeleteReviewOfSystemByEntityAsync(ReviewOfSystem reviewOfSystem, Guid? OrgID, bool Subscription);
    }
}
