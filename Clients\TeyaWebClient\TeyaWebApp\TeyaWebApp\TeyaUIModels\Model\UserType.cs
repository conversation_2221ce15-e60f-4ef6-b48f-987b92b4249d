﻿namespace TeyaWebApp.Model
{
    public class UserType
    {
        public Guid Id { get; set; } // UserId renamed to Id and set as Guid
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? LastName { get; set; }
        public string? Suffix { get; set; }
        public string? FederalTaxId { get; set; }
        public string? DEANumber { get; set; }
        public string? UPIN { get; set; }
        public string? NPI { get; set; }
        public string? ProviderType { get; set; }
        public string? MainMenuRole { get; set; }
        public string? PatientMenuRole { get; set; }
        public string? Supervisor { get; set; }
        public string? JobDescription { get; set; }
        public string? Taxonomy { get; set; }
        public string? NewCropERxRole { get; set; }
        public string? AccessControl { get; set; }
        public string? GoogleEmail { get; set; }
        public string? AdditionalInfo { get; set; }
        public string? DefaultBillingFacility { get; set; }
    }
}
