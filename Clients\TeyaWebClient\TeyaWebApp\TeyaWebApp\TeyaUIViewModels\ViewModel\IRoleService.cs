﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace BusinessLayer.Services
{
    public interface IRoleService
    {
        Task<Role> RegisterRoleAsync(Role role);
        Task<Role> GetRoleByIdAsync(Guid roleId, Guid OrgID, bool Subscription);
        Task<List<Role>> GetAllRolesAsync();
        Task DeleteRoleByIdAsync(Guid roleId, Guid OrgID, bool Subscription);
        Task UpdateRoleByIdAsync(Guid roleId, Role role);
        Task<List<Role>> GetRolesByNameAsync(string name, Guid OrgID, bool Subscription);
        Task<List<Role>> GetAllRolesByOrgIdAsync(Guid? ID, bool Subscription);
    }
}
