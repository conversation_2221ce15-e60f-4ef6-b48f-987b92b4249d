﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public class DiagnosticImagingPageService : IDiagnosticImagingPageService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<DiagnosticImagingPageService> _localizer;
        private readonly ILogger<DiagnosticImagingPageService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public DiagnosticImagingPageService(HttpClient httpClient, IConfiguration configuration, ILogger<DiagnosticImagingPageService> logger, IStringLocalizer<DiagnosticImagingPageService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            List<DiagnosticImagingDTO> result = new();

            try
            {
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/{id}/isActive/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<DiagnosticImagingDTO>>() ?? new();
                }
                else
                {
                    var errorMessage = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to fetch diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message} for ID: {Id}"], _localizer["AddressRetrievalFailure"], id);
            }

            return result;
        }


        /// <summary>
        ///  Get Assessment by Id 
        /// </summary>
        public async Task<List<DiagnosticImagingAssessment>> GetAssessmentyById(Guid id, Guid? OrgID, bool Subscription)
        {
            List<DiagnosticImagingAssessment> result = new();

            try
            {
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/Assessment/{id}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<DiagnosticImagingAssessment>>() ?? new();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to fetch assessment. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message} for ID: {Id}"], _localizer["AddressRetrievalFailure"], id);
            }

            return result;
        }


        /// <summary>
        ///  Get All Diagnostic Imaging by Id 
        /// </summary>
        public async Task<List<DiagnosticImagingDTO>> GetDiagnosticImagingAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            List<DiagnosticImagingDTO> result = new();

            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/{id}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    result = await response.Content.ReadFromJsonAsync<List<DiagnosticImagingDTO>>() ?? new();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to retrieve diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message} for ID: {Id}"], _localizer["MemberRetrievalFailure"], id);
            }

            return result;
        }


        /// <summary>
        /// Add new DiagnosticImaging
        /// </summary>
        public async Task CreateDiagnosticImagingAsync(List<DiagnosticImagingDTO> member, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(member);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully created diagnostic imaging records."]);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to create diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["DiagnosticImagingRetrievalFailure"]);
            }
        }


        /// <summary>
        /// Add new Assessment
        /// </summary>
        public async Task CreateAssessmentAsync(List<DiagnosticImagingAssessment> member, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(member);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/Assessment/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully created diagnostic imaging assessments."]);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to create assessment. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["DiagnosticImagingCreationFailure"]);
            }
        }


        /// <summary>
        /// Update an existing DiagnosticImaging
        /// </summary>
        public async Task UpdateDiagnosticImagingAsync(DiagnosticImagingDTO diagnosticImaging, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/{diagnosticImaging.RecordID}/{OrgID}/{Subscription}";
                var bodyContent = System.Text.Json.JsonSerializer.Serialize(diagnosticImaging);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully updated diagnostic imaging with RecordID: {RecordID}"], diagnosticImaging.RecordID);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to update diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message} for RecordID: {RecordID}"], _localizer["ErrorUpdatingDiagnosticImaging"], diagnosticImaging.RecordID);
            }
        }


        /// <summary>
        ///  Delete an existing DiagnosticImaging By Id
        /// </summary>
        public async Task DeleteDiagnosticImagingAsync(Guid MemberId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/{MemberId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully deleted diagnostic imaging for MemberId: {MemberId}"], MemberId);
                }
                else
                {
                    var errorMessage = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to delete diagnostic imaging. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, errorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message} for MemberId: {MemberId}"], _localizer["ErrorDeletingDiagnosticImaging"], MemberId);
            }
        }


        /// <summary>
        ///  Update an existing List of DiagnosticImaging
        /// </summary>
        public async Task UpdateDiagnosticImagingList(List<DiagnosticImagingDTO> diagnosticImagings, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/DiagnosticImagingPage/updateDiagnosticImaging/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var bodyContent = System.Text.Json.JsonSerializer.Serialize(diagnosticImagings);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["Successfully updated diagnostic imaging list."]);
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();
                    _logger.LogWarning(_localizer["Failed to update diagnostic imaging list. StatusCode: {StatusCode}, Error: {Error}"],
                        response.StatusCode, error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["{Message}"], _localizer["UpdateDiagnosticImagingListFailure"]);
            }
        }

    }
}