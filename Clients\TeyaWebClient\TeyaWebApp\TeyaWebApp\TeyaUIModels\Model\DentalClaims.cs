﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class DentalClaims
    {
        public Guid Id { get; set; }
        public string? ClaimNumber { get; set; }
        public string? PatientInfo { get; set; }
        public string? FacilityInfo { get; set; }
        public string? OrthodonticsTreatmentServiceCommenced { get; set; }
        public DateTime? OrthodonticsTreatmentDate { get; set; }
        public string? OrthodonticsTreatmentRemaining { get; set; }

        public DateTime? ClaimDate { get; set; }

        public DateTime? ServiceDate { get; set; }

        public bool DentistPretreatmentEstimates { get; set; }

        public bool DentistStatementOfActualServices { get; set; }

        public bool MedicaidClaim { get; set; }

        public bool EPSDT { get; set; }

        public string? PriorAuthorizationNumber { get; set; }

        public string? RadiographsDetails { get; set; }

        public Guid? OrganizationID { get; set; }

        public bool? Subscription { get; set; }
        public bool IsOccupationalInjury { get; set; }
        public bool IsAutoAccident { get; set; }
        public bool IsOtherAccident { get; set; }
        public string? DentistInfo { get; set; }
        public bool IsProsthesis { get; set; }
        public string? ReplacementReason { get; set; }
        public DateTime? PriorDate { get; set; }
        public bool OrthodonticsTreatment { get; set; }
        public string? PlaceOfTreatment { get; set; } = "Office";
        public bool HasRadiographs { get; set; }
        public string? Status { get; set; } = "Pending";
        public bool BillToPatient { get; set; }
        public decimal Copay { get; set; }
        public decimal PatientUncoveredAmount { get; set; }
        public decimal PatientCharges { get; set; }
        public decimal PatientPayments { get; set; }
        public decimal PatientBalance { get; set; }
        public decimal TotalCharges { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal TotalBalance { get; set; }
        public string? OccupationalInjuryDetails { get; set; }
        public string? AutoAccidentDetails { get; set; }
        public string? OtherAccidentDetails { get; set; }
        public enum Source { FDB, CMS }
        public bool IsActive { get; set; } = true;

        private List<string> ICDName = new List<string>();

        public void CalculateBalances()
        {
            PatientBalance = PatientCharges - PatientPayments;
            TotalBalance = TotalCharges - TotalPayments;
        }
    }
}
