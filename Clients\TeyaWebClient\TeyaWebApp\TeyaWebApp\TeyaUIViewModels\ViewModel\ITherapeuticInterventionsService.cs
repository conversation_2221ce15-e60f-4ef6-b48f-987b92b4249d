﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ITherapeuticInterventionsService
    {
        Task<List<TherapeuticInterventionsData>> GetAllByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<TherapeuticInterventionsData>> GetAllByIdAndIsActiveAsync(Guid id, Guid? OrgID, bool Subscription);
        Task AddTherapeuticInterventionsAsync(List<TherapeuticInterventionsData> medicalHistories, Guid? OrgID, bool Subscription);
        Task UpdateTherapeuticInterventionsAsync(TherapeuticInterventionsData _TherapeuticInterventions, Guid? OrgID, bool Subscription);
        Task UpdateTherapeuticInterventionsListAsync(List<TherapeuticInterventionsData> medicalHistories, Guid? OrgID, bool Subscription);
        Task DeleteTherapeuticInterventionsByEntityAsync(TherapeuticInterventionsData _TherapeuticInterventions, Guid? OrgID, bool Subscription);
    }
}
