﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIViewModels.ViewModel
{
    /// <summary>
    /// Hold username and manage state
    /// </summary>
    public class StateContainer
    {
        private string? _username;
        private string? _extractedName;

        public string? Username
        {
            get => _username;
            set
            {
                _username = value;
                NotifyStateChanged();
            }
        }

        public string? ExtractedName
        {
            get => _extractedName;
            set
            {
                _extractedName = value;
                NotifyStateChanged();
            }
        }

        public event Action? OnChange;

        private void NotifyStateChanged() => OnChange?.Invoke();
    }
}
