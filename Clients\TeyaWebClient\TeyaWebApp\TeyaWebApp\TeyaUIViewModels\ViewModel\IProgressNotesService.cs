﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using System.Net.Http;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public interface IProgressNotesService
    {
        Task<List<Record>> GetRecordsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<Record>> GetRecordsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> SaveRecordAsync(Record updatedRecord, Guid? OrgID, bool Subscription);
        Task <HttpResponseMessage> UploadRecordAsync(Record newRecord, Guid? OrgID, bool Subscription);
        Task <Record>  GetRecordByIdAsync(Guid recordId,Guid?OrgId,bool Subscription);
        Task<List<Record>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription);
    }
}
