﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using TeyaUIModels.Model;

namespace TeyaUIModels.Model
{
    public class PatientSpecificAlertsData
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public Guid PatientId { get; set; }

        [Required]
        [StringLength(200)]
        public string AlertName { get; set; }

        [Required]
        public AlertType AlertType { get; set; }

        public DateTime? LastDone { get; set; }

        public DateTime? DueDate { get; set; }

        public int? RecallAfterDays { get; set; }

        public bool IsRecurring { get; set; }

        public string ProviderName { get; set; }

        [StringLength(500)]
        public string Orders { get; set; }

        [StringLength(200)]
        public string Frequency { get; set; }

        [Required]
        public bool IsActive { get; set; }

        public DateTime CreatedDate { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime? UpdatedDate { get; set; }

        public Guid? UpdatedBy { get; set; }
    }
}