﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Patient : IModel
    {
        public Guid Id { get; set; }
        public Guid? OrganizationID { get; set; }
        public string? PCPName { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Sex { get; set; }
        public DateTime? DOB { get; set; }
        public string? Street { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? PostalCode { get; set; }
        public string? PrimaryInsuranceProvider { get; set; }
        public string? PolicyNumber { get; set; }
        public string? PlanName { get; set; }
        public string? GroupNumber { get; set; }
        public string? PatientImageURL { get; set; }
    }
}
